<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 自动导航栏 -->
    <AppNavigation />

    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">我的任务</h1>
        <p class="text-gray-600 mt-2">查看和管理您的任务</p>
      </div>
      <UButton @click="refresh" variant="outline" :loading="pending">
        <UIcon name="i-heroicons-arrow-path" class="mr-2" />
        刷新
      </UButton>
    </div>

    <!-- 筛选器 -->
    <UCard class="mb-6">
      <div class="flex flex-wrap gap-4">
        <USelectMenu
          v-model="selectedProject"
          :options="projectOptions"
          placeholder="选择项目"
          class="w-48"
        />
        <USelectMenu
          v-model="selectedStatus"
          :options="statusOptions"
          placeholder="选择状态"
          class="w-48"
        />
        <UButton
          @click="clearFilters"
          variant="ghost"
          size="sm"
        >
          清除筛选
        </UButton>
      </div>
    </UCard>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl" />
      <span class="ml-2">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <UAlert
      v-else-if="error"
      icon="i-heroicons-exclamation-triangle"
      color="red"
      variant="soft"
      title="加载失败"
      :description="error.message || '获取任务列表失败'"
      class="mb-6"
    />

    <!-- 空状态 -->
    <div v-else-if="filteredTasks.length === 0" class="text-center py-12">
      <UIcon name="i-heroicons-clipboard-document-list" class="text-6xl text-gray-300 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无任务</h3>
      <p class="text-gray-500">{{ tasks.length === 0 ? '您还没有任何任务' : '没有符合筛选条件的任务' }}</p>
    </div>

    <!-- 任务列表 -->
    <div v-else class="space-y-4">
      <UCard
        v-for="task in filteredTasks"
        :key="task.task_id"
        class="hover:shadow-lg transition-shadow cursor-pointer"
        @click="navigateTo(`/student/tasks/${task.task_id}`)"
      >
        <div class="flex justify-between items-start">
          <div class="flex-1">
            <div class="flex items-center mb-2">
              <h3 class="text-lg font-semibold text-gray-900 mr-3">{{ task.title }}</h3>
              <UBadge
                :color="getStatusColor(task.submission_status)"
                variant="soft"
                size="sm"
              >
                {{ getStatusText(task.submission_status) }}
              </UBadge>
            </div>

            <p v-if="task.description" class="text-gray-600 text-sm mb-3 line-clamp-2">
              {{ task.description }}
            </p>

            <div class="flex items-center space-x-4 text-sm text-gray-500">
              <span class="flex items-center">
                <UIcon name="i-heroicons-folder" class="mr-1" />
                {{ task.project_name }}
              </span>
              <span class="flex items-center">
                <UIcon name="i-heroicons-user" class="mr-1" />
                {{ task.teacher_name }}
              </span>
              <span v-if="task.due_date" class="flex items-center">
                <UIcon name="i-heroicons-calendar" class="mr-1" />
                截止：{{ formatDate(task.due_date) }}
              </span>
              <span v-if="task.latest_submission_date" class="flex items-center">
                <UIcon name="i-heroicons-clock" class="mr-1" />
                最后提交：{{ formatDate(task.latest_submission_date) }}
              </span>
            </div>
          </div>
          <UIcon name="i-heroicons-arrow-right" class="text-gray-400 ml-4" />
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 获取查询参数
const route = useRoute()
const router = useRouter()

// 筛选状态
const selectedProject = ref(route.query.project_id ? parseInt(route.query.project_id as string) : null)
const selectedStatus = ref(route.query.status as string || null)

// 获取任务列表数据
const { data: tasksResponse, pending, error, refresh } = await useAuthFetch('/api/student/tasks', {
  server: false,
  query: computed(() => ({
    project_id: selectedProject.value,
    status: selectedStatus.value
  })),
  default: () => ({
    success: true,
    data: []
  })
})

// 提取任务数据
const tasks = computed(() => tasksResponse.value?.data || [])

// 获取项目列表用于筛选
const { data: projectsResponse } = await useAuthFetch('/api/student/projects', {
  server: false,
  default: () => ({ success: true, data: [] })
})

// 项目选项
const projectOptions = computed(() => [
  { label: '全部项目', value: null },
  ...(projectsResponse.value?.data || []).map((project: any) => ({
    label: project.project_name,
    value: project.project_id
  }))
])

// 状态选项
const statusOptions = [
  { label: '全部状态', value: null },
  { label: '未提交', value: 'not_submitted' },
  { label: '进行中', value: 'in_progress' },
  { label: '已提交', value: 'submitted' },
  { label: '已完成', value: 'completed' },
  { label: '需要修改', value: 'needs_revision' }
]

// 筛选后的任务
const filteredTasks = computed(() => {
  let filtered = tasks.value

  if (selectedProject.value) {
    filtered = filtered.filter(task => task.project_id === selectedProject.value)
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(task => task.submission_status === selectedStatus.value)
  }

  return filtered
})

// 清除筛选
const clearFilters = () => {
  selectedProject.value = null
  selectedStatus.value = null
  router.push({ query: {} })
}

// 监听筛选条件变化，更新URL
watch([selectedProject, selectedStatus], () => {
  const query: any = {}
  if (selectedProject.value) query.project_id = selectedProject.value
  if (selectedStatus.value) query.status = selectedStatus.value

  router.push({ query })
})

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'not_submitted':
      return 'gray'
    case 'in_progress':
      return 'blue'
    case 'submitted':
      return 'yellow'
    case 'completed':
      return 'green'
    case 'needs_revision':
      return 'orange'
    default:
      return 'gray'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'not_submitted':
      return '未提交'
    case 'in_progress':
      return '进行中'
    case 'submitted':
      return '已提交'
    case 'completed':
      return '已完成'
    case 'needs_revision':
      return '需要修改'
    default:
      return '未知状态'
  }
}

// Set page title
useHead({
  title: '我的任务 - 学生任务管理系统'
})
</script>
