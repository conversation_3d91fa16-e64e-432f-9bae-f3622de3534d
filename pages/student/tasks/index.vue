<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 自动导航栏 -->
    <AppNavigation />

    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">我的任务</h1>
        <p class="text-gray-600 mt-2">查看和管理您的任务</p>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="space-y-4">
      <UCard class="hover:shadow-lg transition-shadow">
        <div class="flex justify-between items-start">
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">任务 1</h3>
            <p class="text-gray-600 text-sm mb-3">完成项目的第一阶段开发</p>
            <div class="flex items-center text-sm text-gray-500">
              <UIcon name="i-heroicons-calendar" class="mr-1" />
              截止时间: 2024-01-15
            </div>
          </div>
          <UBadge color="yellow" variant="soft">进行中</UBadge>
        </div>
      </UCard>

      <UCard class="hover:shadow-lg transition-shadow">
        <div class="flex justify-between items-start">
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">任务 2</h3>
            <p class="text-gray-600 text-sm mb-3">编写项目文档</p>
            <div class="flex items-center text-sm text-gray-500">
              <UIcon name="i-heroicons-calendar" class="mr-1" />
              截止时间: 2024-01-20
            </div>
          </div>
          <UBadge color="green" variant="soft">已完成</UBadge>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// Set page title
useHead({
  title: '我的任务 - 学生任务管理系统'
})
</script>
