<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 自动导航栏 -->
    <AppNavigation />

    <div class="flex justify-between items-start mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">任务详情</h1>
        <p class="text-gray-600 mt-2">查看任务详细信息和提交进度</p>
      </div>
      
      <div class="flex space-x-3">
        <UButton color="primary">
          提交作业
        </UButton>
      </div>
    </div>

    <!-- 任务信息 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <UCard class="lg:col-span-2">
        <template #header>
          <h2 class="text-xl font-semibold text-gray-900">任务信息</h2>
        </template>
        
        <div class="space-y-4">
          <div>
            <h3 class="font-medium text-gray-900 mb-2">任务标题</h3>
            <p class="text-gray-600">完成项目的第一阶段开发</p>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-900 mb-2">任务描述</h3>
            <p class="text-gray-600">
              根据项目需求文档，完成系统的基础架构搭建和核心功能开发。
              包括数据库设计、API接口开发和前端页面实现。
            </p>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-900 mb-2">截止时间</h3>
            <p class="text-gray-600">2024年1月15日 23:59</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <template #header>
          <h2 class="text-xl font-semibold text-gray-900">任务状态</h2>
        </template>
        
        <div class="space-y-4">
          <div class="text-center">
            <UBadge color="yellow" variant="soft" size="lg">进行中</UBadge>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-900 mb-2">进度</h3>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-blue-600 h-2 rounded-full" style="width: 60%"></div>
            </div>
            <p class="text-sm text-gray-500 mt-1">60% 完成</p>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-900 mb-2">剩余时间</h3>
            <p class="text-gray-600">5天</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- 提交历史 -->
    <UCard>
      <template #header>
        <h2 class="text-xl font-semibold text-gray-900">提交历史</h2>
      </template>
      
      <div class="space-y-4">
        <div class="border-l-4 border-blue-500 pl-4">
          <div class="flex justify-between items-start">
            <div>
              <h3 class="font-medium text-gray-900">第二次提交</h3>
              <p class="text-gray-600 text-sm">修复了登录功能的bug，完善了用户界面</p>
              <p class="text-gray-500 text-xs mt-1">2024-01-10 14:30</p>
            </div>
            <UBadge color="green" variant="soft">已审阅</UBadge>
          </div>
        </div>
        
        <div class="border-l-4 border-gray-300 pl-4">
          <div class="flex justify-between items-start">
            <div>
              <h3 class="font-medium text-gray-900">第一次提交</h3>
              <p class="text-gray-600 text-sm">完成了基础功能开发</p>
              <p class="text-gray-500 text-xs mt-1">2024-01-05 16:45</p>
            </div>
            <UBadge color="yellow" variant="soft">待审阅</UBadge>
          </div>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 获取路由参数
const route = useRoute()
const taskId = route.params.id

// Set page title
useHead({
  title: `任务详情 - 学生任务管理系统`
})
</script>
