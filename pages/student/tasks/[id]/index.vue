<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 自动导航栏 -->
    <AppNavigation />

    <!-- 返回按钮 -->
    <div class="mb-6">
      <UButton
        variant="ghost"
        @click="navigateTo('/student/tasks')"
        class="mb-4"
      >
        <UIcon name="i-heroicons-arrow-left" class="mr-2" />
        返回任务列表
      </UButton>
    </div>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl" />
      <span class="ml-2">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <UAlert
      v-else-if="error"
      icon="i-heroicons-exclamation-triangle"
      color="red"
      variant="soft"
      title="加载失败"
      :description="error.message || '获取任务详情失败'"
      class="mb-6"
    />

    <!-- 任务详情 -->
    <div v-else-if="taskData">
      <div class="flex justify-between items-start mb-8">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">{{ taskData.title }}</h1>
          <p class="text-gray-600 mt-2">{{ taskData.project_name }} - {{ taskData.teacher_name }}</p>
        </div>

        <div class="flex space-x-3">
          <UButton
            v-if="canSubmit"
            @click="showSubmissionModal = true"
            color="primary"
          >
            {{ taskData.latest_submission ? '更新提交' : '提交作业' }}
          </UButton>
          <UBadge
            :color="getStatusColor(taskData.submission_status)"
            variant="soft"
            size="lg"
          >
            {{ getStatusText(taskData.submission_status) }}
          </UBadge>
        </div>
      </div>

      <!-- 任务信息 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <UCard class="lg:col-span-2">
          <template #header>
            <h2 class="text-xl font-semibold text-gray-900">任务信息</h2>
          </template>

          <div class="space-y-4">
            <div>
              <h3 class="font-medium text-gray-900 mb-2">任务描述</h3>
              <p class="text-gray-600 whitespace-pre-wrap">
                {{ taskData.description || '暂无描述' }}
              </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 class="font-medium text-gray-900 mb-2">所属项目</h3>
                <p class="text-gray-600">{{ taskData.project_name }}</p>
              </div>

              <div>
                <h3 class="font-medium text-gray-900 mb-2">指导老师</h3>
                <p class="text-gray-600">{{ taskData.teacher_name }}</p>
              </div>

              <div>
                <h3 class="font-medium text-gray-900 mb-2">创建时间</h3>
                <p class="text-gray-600">{{ formatDate(taskData.created_at) }}</p>
              </div>

              <div>
                <h3 class="font-medium text-gray-900 mb-2">截止时间</h3>
                <p :class="getDueDateClass(taskData.due_date)">
                  {{ taskData.due_date ? formatDate(taskData.due_date) : '无截止时间' }}
                </p>
              </div>
            </div>

            <div v-if="taskData.git_repo_url">
              <h3 class="font-medium text-gray-900 mb-2">Git仓库</h3>
              <a
                :href="taskData.git_repo_url"
                target="_blank"
                class="text-blue-600 hover:text-blue-800 underline flex items-center"
              >
                {{ taskData.git_repo_url }}
                <UIcon name="i-heroicons-arrow-top-right-on-square" class="ml-1" />
              </a>
            </div>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h2 class="text-xl font-semibold text-gray-900">提交状态</h2>
          </template>

          <div class="space-y-4">
            <div class="text-center">
              <UBadge
                :color="getStatusColor(taskData.submission_status)"
                variant="soft"
                size="lg"
              >
                {{ getStatusText(taskData.submission_status) }}
              </UBadge>
            </div>

            <div v-if="taskData.latest_submission">
              <h3 class="font-medium text-gray-900 mb-2">最新提交</h3>
              <div class="space-y-2 text-sm">
                <p class="text-gray-600">
                  <span class="font-medium">提交时间：</span>
                  {{ formatDate(taskData.latest_submission.submission_date) }}
                </p>
                <p v-if="taskData.latest_submission.git_commit_hash" class="text-gray-600">
                  <span class="font-medium">提交哈希：</span>
                  <code class="bg-gray-100 px-1 rounded text-xs">
                    {{ taskData.latest_submission.git_commit_hash.substring(0, 8) }}
                  </code>
                </p>
              </div>
            </div>

            <div v-if="taskData.due_date">
              <h3 class="font-medium text-gray-900 mb-2">剩余时间</h3>
              <p :class="getDueDateClass(taskData.due_date)">
                {{ getRemainingTime(taskData.due_date) }}
              </p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- 最新提交详情 -->
      <UCard v-if="taskData.latest_submission" class="mb-6">
        <template #header>
          <h2 class="text-xl font-semibold text-gray-900">最新提交详情</h2>
        </template>

        <div class="space-y-4">
          <div>
            <h3 class="font-medium text-gray-900 mb-2">进度描述</h3>
            <p class="text-gray-600 whitespace-pre-wrap">
              {{ taskData.latest_submission.progress_description }}
            </p>
          </div>

          <div v-if="taskData.latest_submission.git_commit_hash" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 class="font-medium text-gray-900 mb-2">Git提交哈希</h3>
              <div class="flex items-center space-x-2">
                <code class="bg-gray-100 px-2 py-1 rounded text-sm">
                  {{ taskData.latest_submission.git_commit_hash }}
                </code>
                <UButton
                  v-if="taskData.git_repo_url"
                  variant="ghost"
                  size="xs"
                  @click="openCommitLink"
                >
                  <UIcon name="i-heroicons-arrow-top-right-on-square" />
                </UButton>
              </div>
            </div>

            <div>
              <h3 class="font-medium text-gray-900 mb-2">提交时间</h3>
              <p class="text-gray-600">
                {{ formatDate(taskData.latest_submission.submission_date) }}
              </p>
            </div>
          </div>
        </div>
      </UCard>

      <!-- 教师反馈 -->
      <UCard v-if="taskData.feedback.length > 0">
        <template #header>
          <h2 class="text-xl font-semibold text-gray-900">教师反馈</h2>
        </template>

        <div class="space-y-4">
          <div
            v-for="feedback in taskData.feedback"
            :key="feedback.feedback_id"
            class="border-l-4 border-blue-500 pl-4"
          >
            <div class="flex justify-between items-start mb-2">
              <h3 class="font-medium text-gray-900">{{ feedback.teacher_name }}</h3>
              <span class="text-xs text-gray-500">
                {{ formatDate(feedback.created_at) }}
              </span>
            </div>
            <p class="text-gray-600 whitespace-pre-wrap">{{ feedback.comment }}</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- 提交作业模态框 -->
    <UModal v-model="showSubmissionModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">
            {{ taskData?.latest_submission ? '更新提交' : '提交作业' }}
          </h3>
        </template>

        <UForm
          :schema="submissionSchema"
          :state="submissionForm"
          @submit="submitTask"
          class="space-y-4"
        >
          <UFormGroup label="进度描述" name="progress_description" required>
            <UTextarea
              v-model="submissionForm.progress_description"
              placeholder="请详细描述您的完成情况和遇到的问题..."
              rows="5"
            />
          </UFormGroup>

          <UFormGroup label="Git提交哈希" name="git_commit_hash" required>
            <UInput
              v-model="submissionForm.git_commit_hash"
              placeholder="例如：a1b2c3d4e5f6..."
            />
            <template #help>
              <p class="text-sm text-gray-500">
                请提供您最新代码提交的Git哈希值
              </p>
            </template>
          </UFormGroup>

          <div class="flex justify-end space-x-3">
            <UButton
              variant="ghost"
              @click="showSubmissionModal = false"
              :disabled="submissionLoading"
            >
              取消
            </UButton>
            <UButton
              type="submit"
              :loading="submissionLoading"
            >
              提交
            </UButton>
          </div>
        </UForm>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'

// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 获取路由参数
const route = useRoute()
const taskId = parseInt(route.params.id as string)

// 获取任务详情数据
const { data: taskResponse, pending, error, refresh } = await useAuthFetch(`/api/student/tasks/${taskId}`, {
  server: false,
  default: () => ({
    success: false,
    data: null
  })
})

// 提取任务数据
const taskData = computed(() => taskResponse.value?.data)

// 提交相关状态
const showSubmissionModal = ref(false)
const submissionLoading = ref(false)

// 提交表单验证
const submissionSchema = z.object({
  progress_description: z.string().min(1, '进度描述不能为空').max(2000, '进度描述不能超过2000字符'),
  git_commit_hash: z.string().min(1, 'Git提交哈希不能为空').max(100, 'Git提交哈希格式错误')
})

// 提交表单数据
const submissionForm = reactive({
  progress_description: '',
  git_commit_hash: ''
})

// 监听任务数据变化，更新表单
watch(taskData, (newData) => {
  if (newData?.latest_submission) {
    submissionForm.progress_description = newData.latest_submission.progress_description
    submissionForm.git_commit_hash = newData.latest_submission.git_commit_hash || ''
  }
}, { immediate: true })

// 判断是否可以提交
const canSubmit = computed(() => {
  if (!taskData.value) return false
  const status = taskData.value.submission_status
  return status === 'not_submitted' || status === 'in_progress' || status === 'needs_revision'
})

// 提交任务
const submitTask = async () => {
  try {
    submissionLoading.value = true

    const { data } = await $fetch('/api/student/submissions', {
      method: 'POST',
      body: {
        task_id: taskId,
        progress_description: submissionForm.progress_description,
        git_commit_hash: submissionForm.git_commit_hash
      },
      headers: useRequestHeaders(['authorization'])
    })

    if (data) {
      showSubmissionModal.value = false
      await refresh()

      // 显示成功提示
      const toast = useToast()
      toast.add({
        title: '提交成功',
        description: '您的作业已成功提交，等待老师审阅',
        color: 'green'
      })
    }
  } catch (error: any) {
    console.error('Submit task error:', error)

    const toast = useToast()
    toast.add({
      title: '提交失败',
      description: error.data?.error?.message || '提交作业失败',
      color: 'red'
    })
  } finally {
    submissionLoading.value = false
  }
}

// 打开提交链接
const openCommitLink = () => {
  if (taskData.value?.git_repo_url && taskData.value?.latest_submission?.git_commit_hash) {
    const repoUrl = taskData.value.git_repo_url
    const commitHash = taskData.value.latest_submission.git_commit_hash

    // 构建GitHub/GitLab提交链接
    let commitUrl = ''
    if (repoUrl.includes('github.com')) {
      commitUrl = repoUrl.replace('.git', '') + '/commit/' + commitHash
    } else if (repoUrl.includes('gitlab.com')) {
      commitUrl = repoUrl.replace('.git', '') + '/-/commit/' + commitHash
    } else {
      // 其他Git平台，尝试通用格式
      commitUrl = repoUrl.replace('.git', '') + '/commit/' + commitHash
    }

    window.open(commitUrl, '_blank')
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取截止日期样式
const getDueDateClass = (dueDateString: string | null) => {
  if (!dueDateString) return 'text-gray-600'

  const dueDate = new Date(dueDateString)
  const now = new Date()
  const diffDays = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 3600 * 24))

  if (diffDays < 0) return 'text-red-600 font-medium' // 已过期
  if (diffDays <= 3) return 'text-orange-600 font-medium' // 即将到期
  return 'text-gray-600'
}

// 获取剩余时间
const getRemainingTime = (dueDateString: string) => {
  const dueDate = new Date(dueDateString)
  const now = new Date()
  const diffMs = dueDate.getTime() - now.getTime()
  const diffDays = Math.ceil(diffMs / (1000 * 3600 * 24))

  if (diffDays < 0) return '已过期'
  if (diffDays === 0) return '今天到期'
  if (diffDays === 1) return '明天到期'
  return `${diffDays}天后到期`
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'not_submitted':
      return 'gray'
    case 'in_progress':
      return 'blue'
    case 'submitted':
      return 'yellow'
    case 'completed':
      return 'green'
    case 'needs_revision':
      return 'orange'
    default:
      return 'gray'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'not_submitted':
      return '未提交'
    case 'in_progress':
      return '进行中'
    case 'submitted':
      return '已提交'
    case 'completed':
      return '已完成'
    case 'needs_revision':
      return '需要修改'
    default:
      return '未知状态'
  }
}

// Set page title
useHead({
  title: computed(() => taskData.value ? `${taskData.value.title} - 任务详情` : '任务详情'),
  meta: [
    {
      name: 'description',
      content: computed(() => taskData.value?.description || '任务详情页面')
    }
  ]
})
</script>
