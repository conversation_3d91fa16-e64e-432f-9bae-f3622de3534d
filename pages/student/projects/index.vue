<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 自动导航栏 -->
    <AppNavigation />

    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">我的项目</h1>
        <p class="text-gray-600 mt-2">查看您参与的项目和进度</p>
      </div>
      <UButton @click="refresh" variant="outline" :loading="pending">
        <UIcon name="i-heroicons-arrow-path" class="mr-2" />
        刷新
      </UButton>
    </div>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl" />
      <span class="ml-2">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <UAlert
      v-else-if="error"
      icon="i-heroicons-exclamation-triangle"
      color="red"
      variant="soft"
      title="加载失败"
      :description="error.message || '获取项目列表失败'"
      class="mb-6"
    />

    <!-- 空状态 -->
    <div v-else-if="projects.length === 0" class="text-center py-12">
      <UIcon name="i-heroicons-folder-open" class="text-6xl text-gray-300 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无项目</h3>
      <p class="text-gray-500">您还没有参与任何项目</p>
    </div>

    <!-- 项目列表 -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <UCard
        v-for="project in projects"
        :key="project.project_id"
        class="hover:shadow-lg transition-shadow cursor-pointer"
        @click="navigateTo(`/student/projects/${project.project_id}`)"
      >
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 truncate">{{ project.project_name }}</h3>
            <UIcon name="i-heroicons-arrow-top-right-on-square" class="text-gray-400" />
          </div>
        </template>

        <div class="space-y-3">
          <p class="text-gray-600 text-sm line-clamp-3">
            {{ project.description || '暂无描述' }}
          </p>

          <div class="space-y-2">
            <div class="flex items-center text-sm text-gray-500">
              <UIcon name="i-heroicons-user" class="mr-2" />
              <span>指导老师：{{ project.teacher_name }}</span>
            </div>

            <div class="flex items-center text-sm text-gray-500">
              <UIcon name="i-heroicons-calendar" class="mr-2" />
              <span>创建时间：{{ formatDate(project.created_at) }}</span>
            </div>

            <div class="flex items-center text-sm">
              <UIcon name="i-heroicons-code-bracket" class="mr-2" />
              <span v-if="project.git_repo_url" class="text-green-600">已配置Git仓库</span>
              <span v-else class="text-orange-600">未配置Git仓库</span>
            </div>
          </div>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 获取项目列表数据
const { data: projectsResponse, pending, error, refresh } = await useAuthFetch('/api/student/projects', {
  server: false,
  default: () => ({
    success: true,
    data: []
  })
})

// 提取项目数据
const projects = computed(() => projectsResponse.value?.data || [])

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Set page title
useHead({
  title: '我的项目 - 学生任务管理系统'
})
</script>
