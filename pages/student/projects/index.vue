<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 自动导航栏 -->
    <AppNavigation />

    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">我的项目</h1>
        <p class="text-gray-600 mt-2">查看您参与的项目和进度</p>
      </div>
    </div>

    <!-- 项目列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <UCard class="hover:shadow-lg transition-shadow cursor-pointer">
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900">示例项目 1</h3>
        </template>
        <p class="text-gray-600 text-sm mb-4">这是一个示例项目的描述</p>
        <div class="flex justify-between items-center text-sm text-gray-500">
          <span>进度: 60%</span>
          <span>3个任务</span>
        </div>
      </UCard>

      <UCard class="hover:shadow-lg transition-shadow cursor-pointer">
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900">示例项目 2</h3>
        </template>
        <p class="text-gray-600 text-sm mb-4">这是另一个示例项目的描述</p>
        <div class="flex justify-between items-center text-sm text-gray-500">
          <span>进度: 30%</span>
          <span>5个任务</span>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// Set page title
useHead({
  title: '我的项目 - 学生任务管理系统'
})
</script>
