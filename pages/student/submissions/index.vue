<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 自动导航栏 -->
    <AppNavigation />

    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">提交历史</h1>
        <p class="text-gray-600 mt-2">查看您的所有提交记录和反馈</p>
      </div>
      <UButton @click="refresh" variant="outline" :loading="pending">
        <UIcon name="i-heroicons-arrow-path" class="mr-2" />
        刷新
      </UButton>
    </div>

    <!-- 筛选器 -->
    <UCard class="mb-6">
      <div class="flex flex-wrap gap-4">
        <USelectMenu
          v-model="selectedProject"
          :options="projectOptions"
          placeholder="选择项目"
          class="w-48"
        />
        <USelectMenu
          v-model="selectedTask"
          :options="taskOptions"
          placeholder="选择任务"
          class="w-48"
          :disabled="!selectedProject"
        />
        <USelectMenu
          v-model="selectedStatus"
          :options="statusOptions"
          placeholder="选择状态"
          class="w-48"
        />
        <UButton
          @click="clearFilters"
          variant="ghost"
          size="sm"
        >
          清除筛选
        </UButton>
      </div>
    </UCard>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl" />
      <span class="ml-2">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <UAlert
      v-else-if="error"
      icon="i-heroicons-exclamation-triangle"
      color="red"
      variant="soft"
      title="加载失败"
      :description="error.message || '获取提交历史失败'"
      class="mb-6"
    />

    <!-- 空状态 -->
    <div v-else-if="filteredSubmissions.length === 0" class="text-center py-12">
      <UIcon name="i-heroicons-document-text" class="text-6xl text-gray-300 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无提交记录</h3>
      <p class="text-gray-500">{{ submissions.length === 0 ? '您还没有任何提交记录' : '没有符合筛选条件的提交记录' }}</p>
    </div>

    <!-- 提交历史列表 -->
    <div v-else class="space-y-4">
      <UCard
        v-for="submission in filteredSubmissions"
        :key="submission.submission_id"
        class="hover:shadow-lg transition-shadow"
      >
        <div class="space-y-4">
          <!-- 提交基本信息 -->
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <div class="flex items-center mb-2">
                <h3 class="text-lg font-semibold text-gray-900 mr-3">{{ submission.task_title }}</h3>
                <UBadge
                  :color="getStatusColor(submission.status)"
                  variant="soft"
                  size="sm"
                >
                  {{ getStatusText(submission.status) }}
                </UBadge>
              </div>
              
              <div class="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                <span class="flex items-center">
                  <UIcon name="i-heroicons-folder" class="mr-1" />
                  {{ submission.project_name }}
                </span>
                <span class="flex items-center">
                  <UIcon name="i-heroicons-clock" class="mr-1" />
                  {{ formatDate(submission.submission_date) }}
                </span>
                <span v-if="submission.git_commit_hash" class="flex items-center">
                  <UIcon name="i-heroicons-code-bracket" class="mr-1" />
                  <code class="bg-gray-100 px-1 rounded text-xs">
                    {{ submission.git_commit_hash.substring(0, 8) }}
                  </code>
                </span>
              </div>
            </div>
            
            <div class="flex space-x-2">
              <UButton
                variant="ghost"
                size="sm"
                @click="navigateTo(`/student/tasks/${submission.task_id}`)"
              >
                查看任务
              </UButton>
              <UButton
                v-if="submission.git_repo_url && submission.git_commit_hash"
                variant="ghost"
                size="sm"
                @click="openCommitLink(submission)"
              >
                <UIcon name="i-heroicons-arrow-top-right-on-square" />
              </UButton>
            </div>
          </div>

          <!-- 进度描述 -->
          <div>
            <h4 class="font-medium text-gray-900 mb-2">进度描述</h4>
            <p class="text-gray-600 text-sm whitespace-pre-wrap">{{ submission.progress_description }}</p>
          </div>

          <!-- 展开/收起详情 -->
          <div class="border-t pt-4">
            <UButton
              variant="ghost"
              size="sm"
              @click="toggleSubmissionDetails(submission.submission_id)"
              class="w-full justify-center"
            >
              <span>{{ expandedSubmissions.has(submission.submission_id) ? '收起详情' : '展开详情' }}</span>
              <UIcon
                :name="expandedSubmissions.has(submission.submission_id) ? 'i-heroicons-chevron-up' : 'i-heroicons-chevron-down'"
                class="ml-2"
              />
            </UButton>
          </div>

          <!-- 详细信息（可展开） -->
          <div v-if="expandedSubmissions.has(submission.submission_id)" class="border-t pt-4 space-y-4">
            <!-- Git信息 -->
            <div v-if="submission.git_commit_hash">
              <h4 class="font-medium text-gray-900 mb-2">Git信息</h4>
              <div class="bg-gray-50 p-3 rounded-lg">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-gray-600">提交哈希</p>
                    <code class="text-sm bg-white px-2 py-1 rounded border">
                      {{ submission.git_commit_hash }}
                    </code>
                  </div>
                  <UButton
                    v-if="submission.git_repo_url"
                    variant="outline"
                    size="xs"
                    @click="openCommitLink(submission)"
                  >
                    查看提交
                  </UButton>
                </div>
              </div>
            </div>

            <!-- 反馈信息 -->
            <div>
              <h4 class="font-medium text-gray-900 mb-2">教师反馈</h4>
              <div v-if="submissionFeedback[submission.submission_id]?.length > 0" class="space-y-3">
                <div
                  v-for="feedback in submissionFeedback[submission.submission_id]"
                  :key="feedback.feedback_id"
                  class="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-500"
                >
                  <div class="flex justify-between items-start mb-2">
                    <span class="font-medium text-gray-900">{{ feedback.teacher_name }}</span>
                    <span class="text-xs text-gray-500">{{ formatDate(feedback.created_at) }}</span>
                  </div>
                  <p class="text-gray-700 text-sm whitespace-pre-wrap">{{ feedback.comment }}</p>
                </div>
              </div>
              <p v-else class="text-gray-500 text-sm">暂无反馈</p>
            </div>
          </div>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 筛选状态
const selectedProject = ref<number | null>(null)
const selectedTask = ref<number | null>(null)
const selectedStatus = ref<string | null>(null)

// 展开状态
const expandedSubmissions = ref(new Set<number>())

// 获取提交历史数据
const { data: submissionsResponse, pending, error, refresh } = await useAuthFetch('/api/student/submissions', {
  server: false,
  query: computed(() => ({
    project_id: selectedProject.value,
    task_id: selectedTask.value,
    status: selectedStatus.value
  })),
  default: () => ({
    success: true,
    data: []
  })
})

// 提取提交数据
const submissions = computed(() => submissionsResponse.value?.data || [])

// 获取项目列表用于筛选
const { data: projectsResponse } = await useAuthFetch('/api/student/projects', {
  server: false,
  default: () => ({ success: true, data: [] })
})

// 获取任务列表用于筛选
const { data: tasksResponse } = await useAuthFetch('/api/student/tasks', {
  server: false,
  query: computed(() => ({
    project_id: selectedProject.value
  })),
  default: () => ({ success: true, data: [] })
})

// 项目选项
const projectOptions = computed(() => [
  { label: '全部项目', value: null },
  ...(projectsResponse.value?.data || []).map((project: any) => ({
    label: project.project_name,
    value: project.project_id
  }))
])

// 任务选项
const taskOptions = computed(() => [
  { label: '全部任务', value: null },
  ...(tasksResponse.value?.data || [])
    .filter((task: any) => !selectedProject.value || task.project_id === selectedProject.value)
    .map((task: any) => ({
      label: task.title,
      value: task.task_id
    }))
])

// 状态选项
const statusOptions = [
  { label: '全部状态', value: null },
  { label: '进行中', value: 'in_progress' },
  { label: '已提交', value: 'submitted' },
  { label: '已完成', value: 'completed' },
  { label: '需要修改', value: 'needs_revision' }
]

// 筛选后的提交
const filteredSubmissions = computed(() => {
  let filtered = submissions.value

  if (selectedProject.value) {
    // 通过项目名称筛选（因为API返回的是project_name而不是project_id）
    const selectedProjectName = projectOptions.value.find(p => p.value === selectedProject.value)?.label
    if (selectedProjectName && selectedProjectName !== '全部项目') {
      filtered = filtered.filter(submission => submission.project_name === selectedProjectName)
    }
  }

  if (selectedTask.value) {
    filtered = filtered.filter(submission => submission.task_id === selectedTask.value)
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(submission => submission.status === selectedStatus.value)
  }

  return filtered.sort((a, b) => new Date(b.submission_date).getTime() - new Date(a.submission_date).getTime())
})

// 提交反馈数据（模拟，实际应该从API获取）
const submissionFeedback = ref<Record<number, any[]>>({})

// 清除筛选
const clearFilters = () => {
  selectedProject.value = null
  selectedTask.value = null
  selectedStatus.value = null
}

// 监听项目变化，清除任务筛选
watch(selectedProject, () => {
  selectedTask.value = null
})

// 切换提交详情展开状态
const toggleSubmissionDetails = (submissionId: number) => {
  if (expandedSubmissions.value.has(submissionId)) {
    expandedSubmissions.value.delete(submissionId)
  } else {
    expandedSubmissions.value.add(submissionId)
  }
}

// 打开提交链接
const openCommitLink = (submission: any) => {
  if (submission.git_repo_url && submission.git_commit_hash) {
    const repoUrl = submission.git_repo_url
    const commitHash = submission.git_commit_hash
    
    // 构建GitHub/GitLab提交链接
    let commitUrl = ''
    if (repoUrl.includes('github.com')) {
      commitUrl = repoUrl.replace('.git', '') + '/commit/' + commitHash
    } else if (repoUrl.includes('gitlab.com')) {
      commitUrl = repoUrl.replace('.git', '') + '/-/commit/' + commitHash
    } else {
      // 其他Git平台，尝试通用格式
      commitUrl = repoUrl.replace('.git', '') + '/commit/' + commitHash
    }
    
    window.open(commitUrl, '_blank')
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'in_progress':
      return 'blue'
    case 'submitted':
      return 'yellow'
    case 'completed':
      return 'green'
    case 'needs_revision':
      return 'orange'
    default:
      return 'gray'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'in_progress':
      return '进行中'
    case 'submitted':
      return '已提交'
    case 'completed':
      return '已完成'
    case 'needs_revision':
      return '需要修改'
    default:
      return '未知状态'
  }
}

// Set page title
useHead({
  title: '提交历史 - 学生任务管理系统'
})
</script>
