<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">
          学生仪表盘
        </h1>
        <p class="text-gray-600 mt-2">
          欢迎回来，{{ user?.full_name || user?.username }}
        </p>
      </div>
      <UButton @click="handleLogout" variant="outline">
        登出
      </UButton>
    </div>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl" />
      <span class="ml-2">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <UAlert
      v-else-if="error"
      icon="i-heroicons-exclamation-triangle"
      color="red"
      variant="soft"
      title="加载失败"
      :description="error.message || '获取仪表盘数据失败'"
      class="mb-6"
    />



    <!-- 仪表盘内容 -->
    <div v-else>
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">我的项目</h3>
          </template>
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600">{{ dashboardData.summary.total_projects }}</div>
            <div class="text-sm text-gray-500">参与项目</div>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">进行中任务</h3>
          </template>
          <div class="text-center">
            <div class="text-3xl font-bold text-orange-600">{{ dashboardData.summary.in_progress_tasks }}</div>
            <div class="text-sm text-gray-500">待完成</div>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">已完成任务</h3>
          </template>
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600">{{ dashboardData.summary.completed_tasks }}</div>
            <div class="text-sm text-gray-500">已提交</div>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">待反馈</h3>
          </template>
          <div class="text-center">
            <div class="text-3xl font-bold text-purple-600">{{ dashboardData.summary.pending_feedback }}</div>
            <div class="text-sm text-gray-500">等待审阅</div>
          </div>
        </UCard>
      </div>

      <!-- 即将到期的任务提醒 -->
      <UCard v-if="dashboardData.upcoming_deadlines.length > 0" class="mb-6">
        <template #header>
          <div class="flex items-center">
            <UIcon name="i-heroicons-clock" class="text-red-500 mr-2" />
            <h3 class="text-lg font-semibold text-red-600">即将到期的任务</h3>
          </div>
        </template>
        <div class="space-y-3">
          <div
            v-for="task in dashboardData.upcoming_deadlines"
            :key="task.task_id"
            class="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200"
          >
            <div>
              <h4 class="font-medium text-gray-900">{{ task.title }}</h4>
              <p class="text-sm text-gray-600">{{ task.project_name }}</p>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-red-600">
                {{ task.days_remaining === 0 ? '今天到期' : `${task.days_remaining}天后到期` }}
              </div>
              <div class="text-xs text-gray-500">
                {{ formatDate(task.due_date) }}
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 进行中的任务 -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold">进行中的任务</h3>
              <UButton
                variant="ghost"
                size="sm"
                @click="navigateTo('/student/tasks')"
              >
                查看全部
              </UButton>
            </div>
          </template>
          <div v-if="dashboardData.tasks.in_progress.length === 0" class="text-center py-8 text-gray-500">
            暂无进行中的任务
          </div>
          <div v-else class="space-y-3">
            <div
              v-for="task in dashboardData.tasks.in_progress.slice(0, 3)"
              :key="task.task_id"
              class="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
              @click="navigateTo(`/student/tasks/${task.task_id}`)"
            >
              <h4 class="font-medium text-gray-900">{{ task.title }}</h4>
              <p class="text-sm text-gray-600 mt-1">{{ task.project_name }}</p>
              <div class="flex items-center justify-between mt-2">
                <UBadge
                  :color="getStatusColor(task.submission_status)"
                  variant="soft"
                  size="sm"
                >
                  {{ getStatusText(task.submission_status) }}
                </UBadge>
                <span v-if="task.due_date" class="text-xs text-gray-500">
                  {{ formatDate(task.due_date) }}
                </span>
              </div>
            </div>
          </div>
        </UCard>

        <!-- 需要修改的任务 -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold">需要修改的任务</h3>
              <UButton
                variant="ghost"
                size="sm"
                @click="navigateTo('/student/tasks?status=needs_revision')"
              >
                查看全部
              </UButton>
            </div>
          </template>
          <div v-if="dashboardData.tasks.needs_revision.length === 0" class="text-center py-8 text-gray-500">
            暂无需要修改的任务
          </div>
          <div v-else class="space-y-3">
            <div
              v-for="task in dashboardData.tasks.needs_revision.slice(0, 3)"
              :key="task.task_id"
              class="p-3 border border-orange-200 rounded-lg hover:bg-orange-50 cursor-pointer"
              @click="navigateTo(`/student/tasks/${task.task_id}`)"
            >
              <h4 class="font-medium text-gray-900">{{ task.title }}</h4>
              <p class="text-sm text-gray-600 mt-1">{{ task.project_name }}</p>
              <div class="flex items-center justify-between mt-2">
                <UBadge color="orange" variant="soft" size="sm">
                  需要修改
                </UBadge>
                <span v-if="task.due_date" class="text-xs text-gray-500">
                  {{ formatDate(task.due_date) }}
                </span>
              </div>
            </div>
          </div>
        </UCard>
      </div>

      <!-- 最新提交和反馈 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <!-- 最新提交 -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold">最新提交</h3>
              <UButton
                variant="ghost"
                size="sm"
                @click="navigateTo('/student/submissions')"
              >
                查看全部
              </UButton>
            </div>
          </template>
          <div v-if="dashboardData.recent_submissions.length === 0" class="text-center py-8 text-gray-500">
            暂无提交记录
          </div>
          <div v-else class="space-y-3">
            <div
              v-for="submission in dashboardData.recent_submissions"
              :key="submission.submission_id"
              class="p-3 border border-gray-200 rounded-lg"
            >
              <h4 class="font-medium text-gray-900">{{ submission.task_title }}</h4>
              <p class="text-sm text-gray-600 mt-1">{{ submission.project_name }}</p>
              <div class="flex items-center justify-between mt-2">
                <UBadge
                  :color="getStatusColor(submission.status)"
                  variant="soft"
                  size="sm"
                >
                  {{ getStatusText(submission.status) }}
                </UBadge>
                <span class="text-xs text-gray-500">
                  {{ formatDate(submission.submission_date) }}
                </span>
              </div>
            </div>
          </div>
        </UCard>

        <!-- 最新反馈 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">最新反馈</h3>
          </template>
          <div v-if="dashboardData.recent_feedback.length === 0" class="text-center py-8 text-gray-500">
            暂无反馈
          </div>
          <div v-else class="space-y-3">
            <div
              v-for="feedback in dashboardData.recent_feedback"
              :key="feedback.feedback_id"
              class="p-3 border border-gray-200 rounded-lg"
            >
              <h4 class="font-medium text-gray-900">{{ feedback.task_title }}</h4>
              <p class="text-sm text-gray-600 mt-1">{{ feedback.project_name }}</p>
              <p class="text-sm text-gray-700 mt-2 line-clamp-2">{{ feedback.comment }}</p>
              <div class="flex items-center justify-between mt-2">
                <span class="text-xs text-gray-500">{{ feedback.teacher_name }}</span>
                <span class="text-xs text-gray-500">
                  {{ formatDate(feedback.created_at) }}
                </span>
              </div>
            </div>
          </div>
        </UCard>
      </div>

      <!-- 快速操作 -->
      <div class="mt-6">
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">快速操作</h3>
          </template>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
            <UButton
              class="w-full"
              variant="outline"
              @click="navigateTo('/student/projects')"
            >
              查看项目
            </UButton>
            <UButton
              class="w-full"
              variant="outline"
              @click="navigateTo('/student/tasks')"
            >
              我的任务
            </UButton>
            <UButton
              class="w-full"
              variant="outline"
              @click="navigateTo('/student/profile')"
            >
              个人设置
            </UButton>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// Auth store
const authStore = useAuthStore()
const user = computed(() => authStore.user)

// 获取仪表盘数据
const { data: dashboardResponse, pending, error } = await useAuthFetch('/api/student/dashboard', {
  server: false
})

// 提取实际的数据
const dashboardData = computed(() => {
  const response = dashboardResponse.value as any
  return response?.data || {
    tasks: { in_progress: [], needs_revision: [] },
    recent_submissions: [],
    recent_feedback: [],
    upcoming_deadlines: [],
    summary: {
      total_projects: 0,
      in_progress_tasks: 0,
      completed_tasks: 0,
      pending_feedback: 0
    }
  }
})

// Handle logout
const handleLogout = async () => {
  await authStore.logout()
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'not_submitted':
      return 'gray'
    case 'in_progress':
      return 'blue'
    case 'submitted':
      return 'yellow'
    case 'completed':
      return 'green'
    case 'needs_revision':
      return 'orange'
    default:
      return 'gray'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'not_submitted':
      return '未提交'
    case 'in_progress':
      return '进行中'
    case 'submitted':
      return '已提交'
    case 'completed':
      return '已完成'
    case 'needs_revision':
      return '需要修改'
    default:
      return '未知状态'
  }
}

// Set page title
useHead({
  title: '学生仪表盘 - 学生任务管理系统'
})
</script>
