<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">
          学生仪表盘
        </h1>
        <p class="text-gray-600 mt-2">
          欢迎回来，{{ user?.full_name || user?.username }}
        </p>
      </div>
      <UButton @click="handleLogout" variant="outline">
        登出
      </UButton>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">我的项目</h3>
        </template>
        <div class="text-center">
          <div class="text-3xl font-bold text-blue-600">0</div>
          <div class="text-sm text-gray-500">参与项目</div>
        </div>
      </UCard>

      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">进行中任务</h3>
        </template>
        <div class="text-center">
          <div class="text-3xl font-bold text-orange-600">0</div>
          <div class="text-sm text-gray-500">待完成</div>
        </div>
      </UCard>

      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">已完成任务</h3>
        </template>
        <div class="text-center">
          <div class="text-3xl font-bold text-green-600">0</div>
          <div class="text-sm text-gray-500">已提交</div>
        </div>
      </UCard>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">近期任务</h3>
        </template>
        <div class="text-center py-8 text-gray-500">
          暂无任务
        </div>
      </UCard>

      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">最新反馈</h3>
        </template>
        <div class="text-center py-8 text-gray-500">
          暂无反馈
        </div>
      </UCard>
    </div>

    <div class="mt-6">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">快速操作</h3>
        </template>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
          <UButton
            class="w-full"
            variant="outline"
            @click="navigateTo('/student/projects')"
          >
            查看项目
          </UButton>
          <UButton
            class="w-full"
            variant="outline"
            @click="navigateTo('/student/tasks')"
          >
            我的任务
          </UButton>
          <UButton
            class="w-full"
            variant="outline"
            @click="navigateTo('/student/profile')"
          >
            个人设置
          </UButton>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// Auth store
const authStore = useAuthStore()
const user = computed(() => authStore.user)

// Handle logout
const handleLogout = async () => {
  await authStore.logout()
}

// Set page title
useHead({
  title: '学生仪表盘 - 学生任务管理系统'
})
</script>
