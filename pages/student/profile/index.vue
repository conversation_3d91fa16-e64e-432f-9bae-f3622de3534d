<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 自动导航栏 -->
    <AppNavigation />

    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">个人资料</h1>
        <p class="text-gray-600 mt-2">管理您的个人信息和设置</p>
      </div>
    </div>

    <!-- 个人信息表单 -->
    <UCard class="max-w-2xl">
      <template #header>
        <h2 class="text-xl font-semibold text-gray-900">基本信息</h2>
      </template>

      <form class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <UFormGroup label="用户名">
            <UInput 
              value="student123" 
              disabled
              placeholder="用户名"
            />
          </UFormGroup>

          <UFormGroup label="邮箱">
            <UInput 
              value="<EMAIL>" 
              type="email"
              placeholder="邮箱地址"
            />
          </UFormGroup>

          <UFormGroup label="姓名">
            <UInput 
              value="张三" 
              placeholder="真实姓名"
            />
          </UFormGroup>

          <UFormGroup label="学号">
            <UInput 
              value="2021001" 
              placeholder="学号"
            />
          </UFormGroup>
        </div>

        <div class="flex justify-end space-x-3 pt-6 border-t">
          <UButton variant="outline">
            重置
          </UButton>
          <UButton color="primary">
            保存更改
          </UButton>
        </div>
      </form>
    </UCard>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// Set page title
useHead({
  title: '个人资料 - 学生任务管理系统'
})
</script>
