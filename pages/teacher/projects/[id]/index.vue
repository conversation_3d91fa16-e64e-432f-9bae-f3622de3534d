<template>
  <div class="container mx-auto px-4 py-8" :key="projectId">
    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl text-blue-600" />
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <UIcon name="i-heroicons-exclamation-triangle" class="text-4xl text-red-500 mb-4" />
      <p class="text-red-600 mb-4">加载项目详情时出现错误</p>
      <UButton @click="refresh()" variant="outline">重试</UButton>
    </div>

    <!-- 项目详情内容 -->
    <div v-else-if="project">
      <!-- 自动导航栏 -->
      <AppNavigation />

      <!-- 页面头部 -->
      <div class="flex justify-between items-start mb-8">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">{{ project.project_name }}</h1>
          <p class="text-gray-600 mt-2">项目详情管理</p>
        </div>
        
        <div class="flex space-x-3">
          <UButton 
            variant="outline" 
            icon="i-heroicons-pencil"
            @click="editProject"
          >
            编辑项目
          </UButton>
          <UButton 
            color="red" 
            variant="outline"
            icon="i-heroicons-trash"
            @click="deleteProject"
          >
            删除项目
          </UButton>
        </div>
      </div>

      <!-- 项目基本信息卡片 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <UCard class="lg:col-span-2">
          <template #header>
            <h2 class="text-xl font-semibold text-gray-900">项目信息</h2>
          </template>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
              <p class="text-gray-900">{{ project.project_name }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">项目描述</label>
              <p class="text-gray-900">
                {{ project.description || '暂无描述' }}
              </p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">创建时间</label>
              <p class="text-gray-900">{{ formatDate(project.created_at) }}</p>
            </div>
          </div>
        </UCard>

        <!-- 统计信息卡片 -->
        <UCard>
          <template #header>
            <h2 class="text-xl font-semibold text-gray-900">项目统计</h2>
          </template>
          
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <UIcon name="i-heroicons-users" class="text-blue-600 mr-2" />
                <span class="text-sm text-gray-600">分配学生</span>
              </div>
              <span class="text-lg font-semibold text-gray-900">{{ project.students?.length || 0 }}</span>
            </div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <UIcon name="i-heroicons-document-text" class="text-green-600 mr-2" />
                <span class="text-sm text-gray-600">项目任务</span>
              </div>
              <span class="text-lg font-semibold text-gray-900">{{ taskCount }}</span>
            </div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <UIcon name="i-heroicons-clipboard-document-check" class="text-purple-600 mr-2" />
                <span class="text-sm text-gray-600">待审核提交</span>
              </div>
              <span class="text-lg font-semibold text-gray-900">{{ pendingSubmissions }}</span>
            </div>
          </div>
        </UCard>
      </div>

      <!-- 学生列表 -->
      <UCard class="mb-8">
        <template #header>
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-900">分配学生 ({{ project.students?.length || 0 }})</h2>
            <UButton 
              variant="outline" 
              icon="i-heroicons-user-plus"
              @click="showAddStudentModal = true"
            >
              添加学生
            </UButton>
          </div>
        </template>

        <div v-if="!project.students?.length" class="text-center py-8">
          <UIcon name="i-heroicons-users" class="text-4xl text-gray-300 mb-4" />
          <p class="text-gray-500 mb-4">暂无分配的学生</p>
          <UButton @click="showAddStudentModal = true" variant="outline">
            添加学生
          </UButton>
        </div>

        <div v-else class="space-y-4">
          <div 
            v-for="student in project.students" 
            :key="student.user_id"
            class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
          >
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <UIcon name="i-heroicons-user" class="text-blue-600" />
              </div>
              <div>
                <h3 class="font-medium text-gray-900">
                  {{ student.full_name || student.username }}
                </h3>
                <p class="text-sm text-gray-500">{{ student.email }}</p>
              </div>
            </div>
            
            <div class="flex items-center space-x-3">
              <div v-if="student.git_repo_url" class="text-sm">
                <a 
                  :href="student.git_repo_url" 
                  target="_blank"
                  class="text-blue-600 hover:text-blue-800 flex items-center"
                >
                  <UIcon name="i-heroicons-link" class="mr-1" />
                  Git仓库
                </a>
              </div>
              <div v-else class="text-sm text-gray-400">
                未设置Git仓库
              </div>
              
              <UDropdown :items="getStudentActions(student)">
                <UButton 
                  variant="ghost" 
                  size="sm" 
                  icon="i-heroicons-ellipsis-vertical"
                />
              </UDropdown>
            </div>
          </div>
        </div>
      </UCard>

      <!-- 项目任务 -->
      <UCard>
        <template #header>
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-900">项目任务</h2>
            <div class="flex space-x-2">
              <UButton
                variant="outline"
                @click="navigateTo('/teacher/tasks')"
              >
                任务管理
              </UButton>
              <UButton
                icon="i-heroicons-plus"
                @click="showCreateTaskModal = true"
              >
                创建任务
              </UButton>
            </div>
          </div>
        </template>

        <div v-if="tasksLoading" class="text-center py-8">
          <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl text-blue-600" />
          <span class="ml-2 text-gray-600">加载任务列表...</span>
        </div>

        <div v-else-if="!projectTasks?.length" class="text-center py-8">
          <UIcon name="i-heroicons-document-text" class="text-4xl text-gray-300 mb-4" />
          <p class="text-gray-500 mb-4">暂无任务</p>
          <UButton @click="showCreateTaskModal = true" variant="outline">
            创建第一个任务
          </UButton>
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="task in projectTasks"
            :key="task.task_id"
            class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
          >
            <div class="flex-1">
              <h3 class="font-medium text-gray-900">{{ task.title }}</h3>
              <p v-if="task.description" class="text-sm text-gray-600 mt-1">{{ task.description }}</p>
              <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                <span v-if="task.due_date">截止时间: {{ formatDate(task.due_date) }}</span>
                <span>创建于: {{ formatDate(task.created_at) }}</span>
              </div>
            </div>

            <div class="flex items-center space-x-3">
              <UBadge
                :color="task.status === 'active' ? 'green' : 'gray'"
                variant="soft"
              >
                {{ task.status === 'active' ? '进行中' : '已结束' }}
              </UBadge>

              <UDropdown :items="getTaskActions(task)">
                <UButton
                  variant="ghost"
                  size="sm"
                  icon="i-heroicons-ellipsis-vertical"
                />
              </UDropdown>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- 项目不存在 -->
    <div v-else class="text-center py-12">
      <UIcon name="i-heroicons-folder-open" class="text-6xl text-gray-300 mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">项目不存在</h3>
      <p class="text-gray-500 mb-4">请检查项目ID是否正确</p>
      <UButton @click="navigateTo('/teacher/projects')" variant="outline">
        返回项目列表
      </UButton>
    </div>

    <!-- 添加学生模态框 -->
    <UModal v-model="showAddStudentModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">添加学生到项目</h3>
        </template>

        <div class="space-y-4">
          <div v-if="availableStudentsLoading" class="text-center py-8">
            <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl text-blue-600" />
            <span class="ml-2 text-gray-600">加载学生列表...</span>
          </div>

          <div v-else-if="availableStudents?.length" class="space-y-2 max-h-60 overflow-y-auto">
            <label
              v-for="student in availableStudents"
              :key="student.user_id"
              class="flex items-center space-x-2 p-3 hover:bg-gray-50 rounded-lg cursor-pointer border"
            >
              <input
                type="checkbox"
                :value="student.user_id"
                v-model="selectedStudentIds"
                :disabled="addingStudents"
                class="rounded"
              />
              <div class="flex-1">
                <div class="font-medium text-gray-900">
                  {{ student.full_name || student.username }}
                </div>
                <div class="text-sm text-gray-500">{{ student.email }}</div>
              </div>
            </label>
          </div>

          <div v-else class="text-center py-8">
            <UIcon name="i-heroicons-users" class="text-4xl text-gray-300 mb-4" />
            <p class="text-gray-500">暂无可添加的学生</p>
            <p class="text-sm text-gray-400 mt-2">所有学生都已分配到此项目</p>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton
              @click="showAddStudentModal = false"
              variant="outline"
              :disabled="addingStudents"
            >
              取消
            </UButton>
            <UButton
              @click="addStudentsToProject"
              color="primary"
              :loading="addingStudents"
              :disabled="selectedStudentIds.length === 0"
            >
              添加学生 ({{ selectedStudentIds.length }})
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>

    <!-- 创建任务模态框 -->
    <UModal v-model="showCreateTaskModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">创建新任务</h3>
        </template>

        <form @submit.prevent="createNewTask" class="space-y-4">
          <UFormGroup label="任务名称" required>
            <UInput
              v-model="taskForm.title"
              placeholder="请输入任务名称"
              :disabled="creatingTask"
            />
          </UFormGroup>

          <UFormGroup label="任务描述">
            <UTextarea
              v-model="taskForm.description"
              placeholder="请输入任务描述（可选）"
              :disabled="creatingTask"
              :rows="3"
            />
          </UFormGroup>

          <UFormGroup label="截止时间">
            <UInput
              v-model="taskForm.due_date"
              type="datetime-local"
              :disabled="creatingTask"
            />
          </UFormGroup>

          <div class="flex justify-end space-x-3 pt-4">
            <UButton
              variant="outline"
              @click="showCreateTaskModal = false"
              :disabled="creatingTask"
            >
              取消
            </UButton>
            <UButton
              type="submit"
              color="primary"
              :loading="creatingTask"
            >
              创建任务
            </UButton>
          </div>
        </form>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 响应式数据
const showAddStudentModal = ref(false)
const addingStudents = ref(false)
const selectedStudentIds = ref<number[]>([])
const showCreateTaskModal = ref(false)
const creatingTask = ref(false)

// 任务表单
const taskForm = ref({
  title: '',
  description: '',
  due_date: ''
})

// 获取路由参数
const route = useRoute()
const projectId = parseInt(route.params.id as string)

// 添加调试信息
console.log('项目详情页面加载，项目ID:', projectId)

// 确保页面在路由变化时重新渲染
watch(() => route.params.id, (newId) => {
  console.log('路由参数变化:', newId)
  if (newId) {
    refresh()
  }
})

// 获取项目详情
const { data: projectResponse, pending, error, refresh } = await useAuthFetch(`/api/teacher/projects/${projectId}`, {
  server: false,
  default: () => ({ success: false, data: null }),
  key: `project-${projectId}` // 添加唯一key确保缓存正确
})

// 获取所有学生列表
const { data: studentsResponse, pending: availableStudentsLoading } = await useAuthFetch('/api/teacher/students', {
  server: false,
  default: () => ({ success: true, data: [] })
})

// 获取项目任务列表
const { data: tasksResponse, pending: tasksLoading, refresh: refreshTasks } = await useAuthFetch(`/api/teacher/tasks?project_id=${projectId}`, {
  server: false,
  default: () => ({ success: true, data: [] })
})

// 提取项目数据
const project = computed(() => (projectResponse.value as any)?.data || null)
const allStudents = computed(() => (studentsResponse.value as any)?.data || [])
const projectTasks = computed(() => (tasksResponse.value as any)?.data || [])

// 计算可添加的学生（排除已分配的学生）
const availableStudents = computed(() => {
  if (!project.value || !allStudents.value) return []
  const assignedStudentIds = project.value.students?.map((s: any) => s.user_id) || []
  return allStudents.value.filter((student: any) => !assignedStudentIds.includes(student.user_id))
})

// 模拟统计数据（后续需要从API获取）
const taskCount = ref(0)
const pendingSubmissions = ref(0)

// 编辑项目
const editProject = () => {
  navigateTo(`/teacher/projects/${projectId}/edit`)
}

// 删除项目
const deleteProject = async () => {
  // TODO: 添加确认对话框
  if (confirm('确定要删除这个项目吗？此操作不可撤销。')) {
    try {
      const accessToken = useCookie('accessToken')
      await $fetch(`/api/teacher/projects/${projectId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${accessToken.value}`
        }
      })

      // 返回项目列表
      navigateTo('/teacher/projects')
    } catch (error) {
      console.error('删除项目失败:', error)
      // TODO: 显示错误消息
    }
  }
}

// 获取任务操作菜单
const getTaskActions = (task: any) => [
  [{
    label: '查看详情',
    icon: 'i-heroicons-eye',
    click: () => navigateTo(`/teacher/tasks/${task.task_id}`)
  }],
  [{
    label: '编辑任务',
    icon: 'i-heroicons-pencil',
    click: () => navigateTo(`/teacher/tasks/${task.task_id}/edit`)
  }],
  [{
    label: '删除任务',
    icon: 'i-heroicons-trash',
    click: () => deleteTask(task.task_id)
  }]
]

// 创建新任务
const createNewTask = async () => {
  creatingTask.value = true
  try {
    const accessToken = useCookie('accessToken')
    await $fetch('/api/teacher/tasks', {
      method: 'POST',
      body: {
        ...taskForm.value,
        project_id: projectId
      },
      headers: {
        'Authorization': `Bearer ${accessToken.value}`
      }
    })

    // 重置表单
    taskForm.value = {
      title: '',
      description: '',
      due_date: ''
    }

    // 关闭模态框
    showCreateTaskModal.value = false

    // 刷新任务列表
    await refreshTasks()

    // TODO: 显示成功消息
  } catch (error) {
    console.error('创建任务失败:', error)
    // TODO: 显示错误消息
  } finally {
    creatingTask.value = false
  }
}

// 删除任务
const deleteTask = async (taskId: number) => {
  if (!confirm('确定要删除这个任务吗？此操作不可撤销。')) return

  try {
    const accessToken = useCookie('accessToken')
    await $fetch(`/api/teacher/tasks/${taskId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${accessToken.value}`
      }
    })

    // 刷新任务列表
    await refreshTasks()

    // TODO: 显示成功消息
  } catch (error) {
    console.error('删除任务失败:', error)
    // TODO: 显示错误消息
  }
}

// 获取学生操作菜单
const getStudentActions = (student: any) => [
  [{
    label: '查看提交',
    icon: 'i-heroicons-document-text',
    click: () => navigateTo(`/teacher/submissions?student_id=${student.user_id}&project_id=${projectId}`)
  }],
  [{
    label: '移除学生',
    icon: 'i-heroicons-user-minus',
    click: () => removeStudent(student.user_id)
  }]
]

// 添加学生到项目
const addStudentsToProject = async () => {
  if (selectedStudentIds.value.length === 0) return

  addingStudents.value = true
  try {
    const accessToken = useCookie('accessToken')

    // 获取当前项目的学生ID列表
    const currentStudentIds = project.value?.students?.map((s: any) => s.user_id) || []

    // 合并当前学生和新选择的学生
    const updatedStudentIds = [...currentStudentIds, ...selectedStudentIds.value]

    await $fetch(`/api/teacher/projects/${projectId}`, {
      method: 'PUT',
      body: {
        student_ids: updatedStudentIds
      },
      headers: {
        'Authorization': `Bearer ${accessToken.value}`
      }
    })

    // 重置选择状态
    selectedStudentIds.value = []
    showAddStudentModal.value = false

    // 刷新项目数据
    await refresh()

    // TODO: 显示成功消息
  } catch (error) {
    console.error('添加学生失败:', error)
    // TODO: 显示错误消息
  } finally {
    addingStudents.value = false
  }
}

// 移除学生
const removeStudent = async (studentId: number) => {
  if (!confirm('确定要从项目中移除这个学生吗？')) return

  try {
    const accessToken = useCookie('accessToken')

    // 获取当前项目的学生ID列表，移除指定学生
    const currentStudentIds = project.value?.students?.map((s: any) => s.user_id) || []
    const updatedStudentIds = currentStudentIds.filter((id: number) => id !== studentId)

    await $fetch(`/api/teacher/projects/${projectId}`, {
      method: 'PUT',
      body: {
        student_ids: updatedStudentIds
      },
      headers: {
        'Authorization': `Bearer ${accessToken.value}`
      }
    })

    // 刷新项目数据
    await refresh()

    // TODO: 显示成功消息
  } catch (error) {
    console.error('移除学生失败:', error)
    // TODO: 显示错误消息
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Set page title
useHead({
  title: computed(() => project.value ? `${project.value.project_name} - 项目详情` : '项目详情'),
  meta: [
    { name: 'description', content: '查看和管理项目详情、学生分配和任务' }
  ]
})
</script>
