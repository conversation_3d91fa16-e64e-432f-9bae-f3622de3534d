<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl text-blue-600" />
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <UIcon name="i-heroicons-exclamation-triangle" class="text-4xl text-red-500 mb-4" />
      <p class="text-red-600 mb-4">加载项目信息时出现错误</p>
      <UButton @click="refresh()" variant="outline">重试</UButton>
    </div>

    <!-- 编辑表单 -->
    <div v-else-if="project">
      <!-- 自动导航栏 -->
      <AppNavigation />

      <!-- 页面头部 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">编辑项目</h1>
        <p class="text-gray-600 mt-2">修改项目信息和学生分配</p>
      </div>

      <!-- 编辑表单 -->
      <UCard class="max-w-4xl">
        <template #header>
          <h2 class="text-xl font-semibold text-gray-900">项目信息</h2>
        </template>

        <form @submit.prevent="updateProject" class="space-y-6">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 基本信息 -->
            <div class="space-y-4">
              <UFormGroup label="项目名称" required>
                <UInput 
                  v-model="editForm.project_name" 
                  placeholder="请输入项目名称"
                  :disabled="updating"
                />
              </UFormGroup>

              <UFormGroup label="项目描述">
                <UTextarea 
                  v-model="editForm.description" 
                  placeholder="请输入项目描述（可选）"
                  :disabled="updating"
                  :rows="4"
                />
              </UFormGroup>
            </div>

            <!-- 学生分配 -->
            <div class="space-y-4">
              <UFormGroup label="分配学生">
                <div v-if="studentsLoading" class="text-center py-4">
                  <UIcon name="i-heroicons-arrow-path" class="animate-spin" />
                  加载学生列表...
                </div>
                <div v-else-if="students?.length" class="space-y-2 max-h-60 overflow-y-auto border rounded-lg p-3">
                  <label 
                    v-for="student in students" 
                    :key="student.user_id"
                    class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer"
                  >
                    <input 
                      type="checkbox" 
                      :value="student.user_id"
                      v-model="editForm.student_ids"
                      :disabled="updating"
                      class="rounded"
                    />
                    <span class="text-sm">
                      {{ student.full_name || student.username }} ({{ student.email }})
                    </span>
                  </label>
                </div>
                <p v-else class="text-gray-500 text-sm">暂无可分配的学生</p>
              </UFormGroup>

              <!-- 当前分配的学生 -->
              <div v-if="editForm.student_ids.length > 0" class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  已选择学生 ({{ editForm.student_ids.length }})
                </label>
                <div class="flex flex-wrap gap-2">
                  <UBadge 
                    v-for="studentId in editForm.student_ids" 
                    :key="studentId"
                    variant="soft"
                    color="blue"
                  >
                    {{ getStudentName(studentId) }}
                  </UBadge>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-end space-x-3 pt-6 border-t">
            <UButton 
              variant="outline" 
              @click="navigateTo(`/teacher/projects/${projectId}`)"
              :disabled="updating"
            >
              取消
            </UButton>
            <UButton 
              type="submit" 
              color="primary"
              :loading="updating"
            >
              保存更改
            </UButton>
          </div>
        </form>
      </UCard>
    </div>

    <!-- 项目不存在 -->
    <div v-else class="text-center py-12">
      <UIcon name="i-heroicons-folder-open" class="text-6xl text-gray-300 mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">项目不存在</h3>
      <p class="text-gray-500 mb-4">请检查项目ID是否正确</p>
      <UButton @click="navigateTo('/teacher/projects')" variant="outline">
        返回项目列表
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 响应式数据
const updating = ref(false)

// 获取路由参数
const route = useRoute()
const projectId = parseInt(route.params.id as string)

// 添加调试信息
console.log('编辑页面加载，项目ID:', projectId)
console.log('路由参数:', route.params)

// 获取项目详情
const { data: projectResponse, pending, error, refresh } = await useAuthFetch(`/api/teacher/projects/${projectId}`, {
  server: false,
  default: () => ({ success: false, data: null }),
  key: `project-edit-${projectId}`,
  onResponseError(context) {
    console.error('获取项目详情失败:', context.response)
  }
})

// 获取学生列表
const { data: studentsResponse, pending: studentsLoading } = await useAuthFetch('/api/teacher/students', {
  server: false,
  default: () => ({ success: true, data: [] })
})

// 提取数据
const project = computed(() => (projectResponse.value as any)?.data || null)
const students = computed(() => (studentsResponse.value as any)?.data || [])

// 编辑表单
const editForm = ref({
  project_name: '',
  description: '',
  student_ids: [] as number[]
})

// 监听项目数据变化，初始化表单
watch(project, (newProject) => {
  if (newProject) {
    editForm.value = {
      project_name: newProject.project_name || '',
      description: newProject.description || '',
      student_ids: newProject.students?.map((s: any) => s.user_id) || []
    }
  }
}, { immediate: true })

// 获取学生姓名
const getStudentName = (studentId: number) => {
  const student = students.value.find((s: any) => s.user_id === studentId)
  return student ? (student.full_name || student.username) : `学生${studentId}`
}

// 更新项目
const updateProject = async () => {
  updating.value = true
  try {
    const accessToken = useCookie('accessToken')
    await $fetch(`/api/teacher/projects/${projectId}`, {
      method: 'PUT',
      body: editForm.value,
      headers: {
        'Authorization': `Bearer ${accessToken.value}`
      }
    })

    // 跳转回项目详情页面
    navigateTo(`/teacher/projects/${projectId}`)
  } catch (error) {
    console.error('更新项目失败:', error)
    // TODO: 显示错误消息
  } finally {
    updating.value = false
  }
}

// Set page title
useHead({
  title: computed(() => project.value ? `编辑 ${project.value.project_name}` : '编辑项目'),
  meta: [
    { name: 'description', content: '编辑项目信息和学生分配' }
  ]
})
</script>
