<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl text-blue-600" />
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <UIcon name="i-heroicons-exclamation-triangle" class="text-4xl text-red-500 mb-4" />
      <h2 class="text-xl font-semibold text-gray-900 mb-2">加载失败</h2>
      <p class="text-gray-600 mb-4">无法加载提交详情</p>
      <UButton @click="navigateTo('/teacher/reviews')">返回审阅列表</UButton>
    </div>

    <!-- 提交详情 -->
    <div v-else-if="submissionData" class="max-w-4xl mx-auto">
      <!-- 自动导航栏 -->
      <AppNavigation />

      <!-- 头部信息 -->
      <div class="flex justify-between items-start mb-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">提交详情</h1>
        </div>

        <div class="flex space-x-3">
          <UButton
            v-if="submissionData.status === 'submitted'"
            color="primary"
            @click="showReviewModal = true"
          >
            <UIcon name="i-heroicons-pencil" class="mr-2" />
            审阅提交
          </UButton>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 左侧：提交信息 -->
        <div class="lg:col-span-2 space-y-6">
          <!-- 基本信息 -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">基本信息</h3>
            </template>
            
            <div class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">学生姓名</label>
                  <p class="text-gray-900">{{ submissionData.student_name }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">学生用户名</label>
                  <p class="text-gray-900">{{ submissionData.student_username }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">任务标题</label>
                  <p class="text-gray-900">{{ submissionData.task_title }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                  <p class="text-gray-900">{{ submissionData.project_name }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">提交时间</label>
                  <p class="text-gray-900">{{ formatDate(submissionData.submission_date) }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                  <UBadge 
                    :color="getStatusColor(submissionData.status)"
                    variant="subtle"
                  >
                    {{ getStatusText(submissionData.status) }}
                  </UBadge>
                </div>
              </div>
            </div>
          </UCard>

          <!-- 进度描述 -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">进度描述</h3>
            </template>
            
            <div class="prose max-w-none">
              <p class="text-gray-700 whitespace-pre-wrap">{{ submissionData.progress_description }}</p>
            </div>
          </UCard>

          <!-- Git信息 -->
          <UCard v-if="submissionData.git_commit_hash || submissionData.git_repo_url">
            <template #header>
              <h3 class="text-lg font-semibold">Git信息</h3>
            </template>
            
            <div class="space-y-3">
              <div v-if="submissionData.git_commit_hash">
                <label class="block text-sm font-medium text-gray-700 mb-1">Commit Hash</label>
                <div class="flex items-center space-x-2">
                  <code class="bg-gray-100 px-2 py-1 rounded text-sm">{{ submissionData.git_commit_hash }}</code>
                  <UButton 
                    v-if="submissionData.git_link"
                    variant="ghost" 
                    size="sm"
                    @click="openGitLink(submissionData.git_link)"
                  >
                    <UIcon name="i-heroicons-arrow-top-right-on-square" />
                  </UButton>
                </div>
              </div>
              
              <div v-if="submissionData.git_repo_url">
                <label class="block text-sm font-medium text-gray-700 mb-1">仓库地址</label>
                <UButton 
                  variant="ghost" 
                  @click="openGitLink(submissionData.git_repo_url)"
                  class="p-0 text-blue-600 hover:text-blue-800"
                >
                  {{ submissionData.git_repo_url }}
                  <UIcon name="i-heroicons-arrow-top-right-on-square" class="ml-1" />
                </UButton>
              </div>
            </div>
          </UCard>
        </div>

        <!-- 右侧：反馈历史 -->
        <div class="space-y-6">
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">反馈历史</h3>
            </template>
            
            <div v-if="!submissionData.feedback?.length" class="text-center py-8 text-gray-500">
              暂无反馈记录
            </div>
            
            <div v-else class="space-y-4">
              <div 
                v-for="feedback in submissionData.feedback" 
                :key="feedback.feedback_id"
                class="border-l-4 border-blue-500 pl-4 py-2"
              >
                <div class="flex justify-between items-start mb-2">
                  <span class="font-medium text-gray-900">{{ feedback.teacher_name }}</span>
                  <span class="text-sm text-gray-500">{{ formatDate(feedback.created_at) }}</span>
                </div>
                <p class="text-gray-700 text-sm whitespace-pre-wrap">{{ feedback.comment }}</p>
              </div>
            </div>
          </UCard>
        </div>
      </div>
    </div>

    <!-- 审阅模态框 -->
    <UModal v-model="showReviewModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">提交反馈</h3>
        </template>

        <form @submit.prevent="submitFeedback" class="space-y-4">
          <UFormGroup label="反馈内容" required>
            <UTextarea 
              v-model="reviewForm.comment" 
              placeholder="请输入您的反馈意见"
              :disabled="submitting"
              rows="6"
            />
          </UFormGroup>

          <UFormGroup label="状态">
            <USelect 
              v-model="reviewForm.status" 
              :options="statusOptions"
              :disabled="submitting"
            />
          </UFormGroup>

          <div class="flex justify-end space-x-3 pt-4">
            <UButton 
              variant="outline" 
              @click="showReviewModal = false"
              :disabled="submitting"
            >
              取消
            </UButton>
            <UButton 
              type="submit" 
              color="primary"
              :loading="submitting"
            >
              提交反馈
            </UButton>
          </div>
        </form>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 获取路由参数
const route = useRoute()
const submissionId = parseInt(route.params.id as string)

// 响应式数据
const showReviewModal = ref(false)
const submitting = ref(false)

// 审阅表单
const reviewForm = ref({
  comment: '',
  status: 'completed' as 'completed' | 'needs_revision'
})

// 状态选项
const statusOptions = [
  { label: '已完成', value: 'completed' },
  { label: '需要修改', value: 'needs_revision' }
]

// 获取提交详情
const { data: submissionResponse, pending, error, refresh } = await useAuthFetch(`/api/teacher/submissions/${submissionId}`, {
  server: false,
  default: () => ({ success: true, data: null })
})

// 提取实际数据
const submissionData = computed(() => submissionResponse.value?.data || null)

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colors = {
    'in_progress': 'blue',
    'submitted': 'yellow',
    'completed': 'green',
    'needs_revision': 'red'
  }
  return colors[status as keyof typeof colors] || 'gray'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const texts = {
    'in_progress': '进行中',
    'submitted': '已提交',
    'completed': '已完成',
    'needs_revision': '需要修改'
  }
  return texts[status as keyof typeof texts] || status
}

// 打开Git链接
const openGitLink = (url: string) => {
  window.open(url, '_blank')
}

// 提交反馈
const submitFeedback = async () => {
  submitting.value = true
  try {
    const accessToken = useCookie('accessToken')
    await $fetch('/api/teacher/feedback', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken.value}`
      },
      body: {
        submission_id: submissionId,
        comment: reviewForm.value.comment,
        status: reviewForm.value.status
      }
    })

    // 关闭模态框
    showReviewModal.value = false
    
    // 刷新数据
    await refresh()
    
    // 重置表单
    reviewForm.value = {
      comment: '',
      status: 'completed'
    }
    
    // TODO: 显示成功消息
  } catch (error) {
    console.error('提交反馈失败:', error)
    // TODO: 显示错误消息
  } finally {
    submitting.value = false
  }
}

// 设置页面标题
useHead({
  title: `提交详情 - 学生任务管理系统`
})
</script>
