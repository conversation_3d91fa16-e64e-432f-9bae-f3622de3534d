<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">提交管理</h1>
        <p class="text-gray-600 mt-2">查看和审核学生提交的作业</p>
      </div>
    </div>

    <!-- 筛选器 -->
    <UCard class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <UFormGroup label="任务筛选">
          <USelect 
            v-model="filters.taskId"
            :options="taskOptions"
            placeholder="选择任务"
            @change="applyFilters"
          />
        </UFormGroup>
        
        <UFormGroup label="项目筛选">
          <USelect 
            v-model="filters.projectId"
            :options="projectOptions"
            placeholder="选择项目"
            @change="applyFilters"
          />
        </UFormGroup>
        
        <UFormGroup label="状态筛选">
          <USelect 
            v-model="filters.status"
            :options="statusOptions"
            placeholder="选择状态"
            @change="applyFilters"
          />
        </UFormGroup>
        
        <UFormGroup label="操作">
          <div class="flex space-x-2">
            <UButton @click="clearFilters" variant="outline" size="sm">
              清除筛选
            </UButton>
            <UButton @click="refreshData" variant="outline" size="sm">
              刷新
            </UButton>
          </div>
        </UFormGroup>
      </div>
    </UCard>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl text-blue-600" />
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <UIcon name="i-heroicons-exclamation-triangle" class="text-4xl text-red-500 mb-4" />
      <p class="text-red-600 mb-4">加载提交列表时出现错误</p>
      <UButton @click="refresh()" variant="outline">重试</UButton>
    </div>

    <!-- 提交列表 -->
    <div v-else>
      <div v-if="!submissions?.length" class="text-center py-12">
        <UIcon name="i-heroicons-document-text" class="text-6xl text-gray-300 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无提交</h3>
        <p class="text-gray-500 mb-4">当前筛选条件下没有找到提交记录</p>
      </div>

      <div v-else class="space-y-4">
        <UCard 
          v-for="submission in submissions" 
          :key="submission.submission_id"
          class="hover:shadow-lg transition-shadow"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <UIcon name="i-heroicons-document-text" class="text-blue-600" />
              </div>
              <div>
                <h3 class="font-medium text-gray-900">{{ submission.task_title }}</h3>
                <p class="text-sm text-gray-600">{{ submission.project_name }}</p>
                <p class="text-sm text-gray-500">
                  提交者: {{ submission.student_name }} ({{ submission.student_email }})
                </p>
                <p v-if="submission.content" class="text-sm text-gray-600 mt-1 line-clamp-2">
                  {{ submission.content }}
                </p>
              </div>
            </div>
            
            <div class="flex items-center space-x-4">
              <div class="text-right">
                <UBadge 
                  :color="getSubmissionStatusColor(submission.status)"
                  variant="soft"
                  class="mb-2"
                >
                  {{ getSubmissionStatusText(submission.status) }}
                </UBadge>
                <p class="text-xs text-gray-500">
                  {{ formatDate(submission.submitted_at) }}
                </p>
              </div>
              
              <UButton 
                variant="outline"
                size="sm"
                @click="viewSubmission(submission.submission_id)"
              >
                查看详情
              </UButton>
            </div>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 响应式数据
const filters = ref({
  taskId: null as number | null,
  projectId: null as number | null,
  status: null as string | null
})

// 获取URL查询参数
const route = useRoute()
if (route.query.task_id) {
  filters.value.taskId = parseInt(route.query.task_id as string)
}
if (route.query.project_id) {
  filters.value.projectId = parseInt(route.query.project_id as string)
}

// 构建查询参数
const queryParams = computed(() => {
  const params: Record<string, string> = {}
  if (filters.value.taskId) params.task_id = filters.value.taskId.toString()
  if (filters.value.projectId) params.project_id = filters.value.projectId.toString()
  if (filters.value.status) params.status = filters.value.status
  return new URLSearchParams(params).toString()
})

// 获取提交列表
const { data: submissionsResponse, pending, error, refresh } = await useAuthFetch(() => {
  const query = queryParams.value
  return `/api/teacher/submissions${query ? `?${query}` : ''}`
}, {
  server: false,
  default: () => ({ success: true, data: [] }),
  key: 'teacher-submissions'
})

// 获取任务列表用于筛选
const { data: tasksResponse } = await useAuthFetch('/api/teacher/tasks', {
  server: false,
  default: () => ({ success: true, data: [] })
})

// 获取项目列表用于筛选
const { data: projectsResponse } = await useAuthFetch('/api/teacher/projects', {
  server: false,
  default: () => ({ success: true, data: [] })
})

// 提取数据
const submissions = computed(() => (submissionsResponse.value as any)?.data || [])
const tasks = computed(() => (tasksResponse.value as any)?.data || [])
const projects = computed(() => (projectsResponse.value as any)?.data || [])

// 筛选选项
const taskOptions = computed(() => [
  { label: '所有任务', value: null },
  ...tasks.value.map((task: any) => ({
    label: task.title,
    value: task.task_id
  }))
])

const projectOptions = computed(() => [
  { label: '所有项目', value: null },
  ...projects.value.map((project: any) => ({
    label: project.project_name,
    value: project.project_id
  }))
])

const statusOptions = [
  { label: '所有状态', value: null },
  { label: '待审核', value: 'pending' },
  { label: '已通过', value: 'approved' },
  { label: '已拒绝', value: 'rejected' }
]

// 应用筛选
const applyFilters = () => {
  refresh()
}

// 清除筛选
const clearFilters = () => {
  filters.value = {
    taskId: null,
    projectId: null,
    status: null
  }
  refresh()
}

// 刷新数据
const refreshData = () => {
  refresh()
}

// 查看提交详情
const viewSubmission = (submissionId: number) => {
  navigateTo(`/teacher/submissions/${submissionId}`)
}

// 获取提交状态颜色
const getSubmissionStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'orange'
    case 'approved': return 'green'
    case 'rejected': return 'red'
    case 'submitted': return 'blue'
    case 'in_progress': return 'gray'
    default: return 'gray'
  }
}

// 获取提交状态文本
const getSubmissionStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待审核'
    case 'approved': return '已通过'
    case 'rejected': return '已拒绝'
    case 'submitted': return '已提交'
    case 'in_progress': return '进行中'
    default: return '未知'
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Set page title
useHead({
  title: '提交管理 - 学生任务管理系统'
})
</script>
