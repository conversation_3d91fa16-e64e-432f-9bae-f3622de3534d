# 自动导航系统使用指南

## 概述

本项目实现了一个自动导航系统，可以根据当前路由自动生成面包屑导航和返回按钮，无需在每个页面手动编写导航代码。

## 核心组件

### 1. AppNavigation 组件

位置：`components/AppNavigation.vue`

这是一个可复用的导航组件，会自动：
- 根据当前路由生成面包屑导航
- 显示返回上级页面的按钮
- 提供正确的返回路径和按钮文本

### 2. 路由配置文件

位置：`utils/routeConfig.ts`

定义了所有页面的层级关系和显示名称。

## 使用方法

### 在页面中使用

只需在页面模板中添加一行代码：

```vue
<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 自动导航栏 -->
    <AppNavigation />
    
    <!-- 页面内容 -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">页面标题</h1>
        <p class="text-gray-600 mt-2">页面描述</p>
      </div>
    </div>
    
    <!-- 其他内容... -->
  </div>
</template>
```

### 添加新页面的导航配置

在 `utils/routeConfig.ts` 中添加新页面的配置：

```typescript
export const routeConfigs: Record<string, RouteConfig> = {
  // 现有配置...
  
  '/teacher/new-page': {
    name: '新页面',
    parent: '/teacher/dashboard',
    backText: '返回首页'
  },
  '/teacher/new-page/[id]': {
    name: '详情页面',
    parent: '/teacher/new-page',
    backText: '返回列表'
  }
}
```

## 配置说明

### RouteConfig 接口

```typescript
interface RouteConfig {
  name: string        // 页面显示名称
  parent?: string     // 父级页面路径（可选）
  backText?: string   // 返回按钮文本（可选，默认为"返回上级"）
}
```

### 动态路由支持

系统支持动态路由（如 `/teacher/tasks/[id]`），会自动处理路径参数的映射。

例如：
- 实际路径：`/teacher/tasks/123`
- 配置路径：`/teacher/tasks/[id]`
- 父级路径：`/teacher/tasks`

## 当前已配置的路由

### 教师路由
- `/teacher/dashboard` - 首页
- `/teacher/projects` - 项目管理
- `/teacher/projects/[id]` - 项目详情
- `/teacher/projects/[id]/edit` - 编辑项目
- `/teacher/tasks` - 任务管理
- `/teacher/tasks/[id]` - 任务详情
- `/teacher/tasks/[id]/edit` - 编辑任务
- `/teacher/reviews` - 进度审阅
- `/teacher/submissions/[id]` - 提交详情
- `/teacher/students` - 学生管理

### 学生路由
- `/student/dashboard` - 首页
- `/student/projects` - 我的项目
- `/student/projects/[id]` - 项目详情
- `/student/tasks` - 我的任务
- `/student/tasks/[id]` - 任务详情
- `/student/submissions` - 提交记录
- `/student/submissions/[id]` - 提交详情
- `/student/profile` - 个人资料

## 优势

1. **一致性**：所有页面的导航样式和行为保持一致
2. **维护性**：只需在一个地方配置路由关系
3. **自动化**：无需在每个页面手动编写导航代码
4. **灵活性**：支持自定义返回按钮文本和父级关系
5. **动态路由**：自动处理带参数的路由

## 扩展

如需添加更多功能，可以在以下文件中进行扩展：
- `components/AppNavigation.vue` - 修改导航组件的样式和行为
- `utils/routeConfig.ts` - 添加新的路由配置或工具函数
