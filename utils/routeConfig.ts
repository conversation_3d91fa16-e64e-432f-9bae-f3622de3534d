export interface RouteConfig {
  name: string
  parent?: string
  backText?: string
}

// 路由配置映射
export const routeConfigs: Record<string, RouteConfig> = {
  // 教师路由
  '/teacher/dashboard': {
    name: '首页'
  },
  '/teacher/projects': {
    name: '项目管理',
    parent: '/teacher/dashboard',
    backText: '返回首页'
  },
  '/teacher/projects/[id]': {
    name: '项目详情',
    parent: '/teacher/projects',
    backText: '返回项目列表'
  },
  '/teacher/projects/[id]/edit': {
    name: '编辑项目',
    parent: '/teacher/projects/[id]',
    backText: '返回项目详情'
  },
  '/teacher/tasks': {
    name: '任务管理',
    parent: '/teacher/dashboard',
    backText: '返回首页'
  },
  '/teacher/tasks/[id]': {
    name: '任务详情',
    parent: '/teacher/tasks',
    backText: '返回任务列表'
  },
  '/teacher/tasks/[id]/edit': {
    name: '编辑任务',
    parent: '/teacher/tasks/[id]',
    backText: '返回任务详情'
  },
  '/teacher/reviews': {
    name: '进度审阅',
    parent: '/teacher/dashboard',
    backText: '返回首页'
  },
  '/teacher/submissions/[id]': {
    name: '提交详情',
    parent: '/teacher/reviews',
    backText: '返回审阅列表'
  },
  '/teacher/students': {
    name: '学生管理',
    parent: '/teacher/dashboard',
    backText: '返回首页'
  },
  
  // 学生路由
  '/student/dashboard': {
    name: '首页'
  },
  '/student/projects': {
    name: '我的项目',
    parent: '/student/dashboard',
    backText: '返回首页'
  },
  '/student/projects/[id]': {
    name: '项目详情',
    parent: '/student/projects',
    backText: '返回项目列表'
  },
  '/student/tasks': {
    name: '我的任务',
    parent: '/student/dashboard',
    backText: '返回首页'
  },
  '/student/tasks/[id]': {
    name: '任务详情',
    parent: '/student/tasks',
    backText: '返回任务列表'
  },
  '/student/submissions': {
    name: '提交记录',
    parent: '/student/dashboard',
    backText: '返回首页'
  },
  '/student/submissions/[id]': {
    name: '提交详情',
    parent: '/student/submissions',
    backText: '返回提交列表'
  },
  '/student/profile': {
    name: '个人资料',
    parent: '/student/dashboard',
    backText: '返回首页'
  }
}

/**
 * 将动态路由转换为配置中的模式
 * 例如: /teacher/tasks/123 -> /teacher/tasks/[id]
 */
export const normalizeRoutePath = (path: string): string => {
  return path.replace(/\/\d+/g, '/[id]')
}

/**
 * 获取指定路由的配置
 */
export const getRouteConfig = (path: string): RouteConfig | null => {
  const normalizedPath = normalizeRoutePath(path)
  return routeConfigs[normalizedPath] || null
}

/**
 * 构建面包屑导航数组
 */
export const buildBreadcrumbs = (currentPath: string): Array<{ name: string; path: string }> => {
  const crumbs: Array<{ name: string; path: string }> = []
  let normalizedPath = normalizeRoutePath(currentPath)
  
  while (normalizedPath && routeConfigs[normalizedPath]) {
    const config = routeConfigs[normalizedPath]
    
    // 对于动态路由，需要还原实际路径
    let actualPath = normalizedPath
    if (normalizedPath.includes('[id]')) {
      actualPath = currentPath
      // 如果是父级路径，需要构建正确的父级实际路径
      if (normalizedPath !== normalizeRoutePath(currentPath)) {
        const currentSegments = currentPath.split('/')
        const normalizedSegments = normalizedPath.split('/')
        const actualSegments = normalizedSegments.map((segment, index) => {
          if (segment === '[id]') {
            return currentSegments[index] || segment
          }
          return segment
        })
        actualPath = actualSegments.join('/')
      }
    }
    
    crumbs.unshift({
      name: config.name,
      path: actualPath
    })
    
    normalizedPath = config.parent || ''
  }
  
  return crumbs
}

/**
 * 获取父级路由路径
 */
export const getParentRoutePath = (currentPath: string): string | null => {
  const config = getRouteConfig(currentPath)
  if (!config?.parent) return null
  
  let parentPath = config.parent
  
  // 如果父级是动态路由，需要构建实际路径
  if (parentPath.includes('[id]')) {
    const currentSegments = currentPath.split('/')
    const parentSegments = parentPath.split('/')
    
    // 构建父级路径
    const actualParentSegments = parentSegments.map((segment, index) => {
      if (segment === '[id]') {
        return currentSegments[index] || segment
      }
      return segment
    })
    
    parentPath = actualParentSegments.join('/')
  }
  
  return parentPath
}
