import { executeQuery } from '../config/database'

async function checkStudentData() {
  console.log('=== 检查学生数据 ===')

  try {
    // 检查学生用户
    console.log('\n1. 学生用户:')
    const students = await executeQuery('SELECT user_id, username, role FROM users WHERE role = "student"')
    console.log(students)

    // 检查项目
    console.log('\n2. 项目:')
    const projects = await executeQuery('SELECT project_id, project_name, teacher_id FROM projects')
    console.log(projects)

    // 检查学生项目关联
    console.log('\n3. 学生项目关联:')
    const studentProjects = await executeQuery('SELECT * FROM student_projects')
    console.log(studentProjects)

    // 检查任务
    console.log('\n4. 任务:')
    const tasks = await executeQuery('SELECT task_id, title, project_id FROM tasks')
    console.log(tasks)

    // 检查提交
    console.log('\n5. 提交:')
    const submissions = await executeQuery('SELECT submission_id, task_id, student_id, status FROM submissions')
    console.log(submissions)

    // 检查特定学生的数据（假设student1的user_id是4）
    const studentId = 4
    console.log(`\n6. 学生ID ${studentId} 的详细数据:`)

    console.log('- 参与的项目:')
    const studentProjectsDetail = await executeQuery(`
      SELECT p.project_id, p.project_name, sp.git_repo_url
      FROM projects p
      JOIN student_projects sp ON p.project_id = sp.project_id
      WHERE sp.student_id = ?
    `, [studentId])
    console.log(studentProjectsDetail)

    console.log('- 相关任务:')
    const studentTasks = await executeQuery(`
      SELECT t.task_id, t.title, t.project_id, p.project_name
      FROM tasks t
      JOIN projects p ON t.project_id = p.project_id
      JOIN student_projects sp ON p.project_id = sp.project_id
      WHERE sp.student_id = ?
    `, [studentId])
    console.log(studentTasks)

    console.log('- 提交记录:')
    const studentSubmissions = await executeQuery(`
      SELECT s.submission_id, s.task_id, s.status, t.title as task_title
      FROM submissions s
      JOIN tasks t ON s.task_id = t.task_id
      WHERE s.student_id = ?
    `, [studentId])
    console.log(studentSubmissions)

  } catch (error) {
    console.error('检查数据时出错:', error)
  }
}

checkStudentData()
