import type { PoolConnection } from 'mysql2/promise'
import { BaseRepository } from './base'
import { safeExecuteQuery, safeExecuteQuerySingle, safeExecuteTransaction } from '../utils/database'

// 项目数据模型
export interface Project {
  project_id: number
  project_name: string
  description: string | null
  teacher_id: number
  created_at: Date
}

// 创建项目数据
export interface CreateProjectData {
  project_name: string
  description?: string
  teacher_id: number
}

// 更新项目数据
export interface UpdateProjectData {
  project_name?: string
  description?: string
}

// 学生-项目关联数据模型
export interface StudentProject {
  student_id: number
  project_id: number
  git_repo_url: string | null
}

// 项目详情（包含教师信息）
export interface ProjectWithTeacher extends Project {
  teacher_name: string
  teacher_email: string
}

// 项目统计信息
export interface ProjectStats {
  project_id: number
  project_name: string
  description: string | null
  created_at: Date
  student_count: number
  task_count: number
}

// 项目仓储类
export class ProjectRepository extends BaseRepository<Project> {
  protected tableName = 'projects'
  protected primaryKey = 'project_id'

  // 根据教师ID查找项目
  async findByTeacherId(teacherId: number): Promise<Project[]> {
    const sql = 'SELECT * FROM projects WHERE teacher_id = ? ORDER BY created_at DESC'
    return safeExecuteQuery<Project>(sql, [teacherId])
  }

  // 创建项目
  async createProject(projectData: CreateProjectData): Promise<Project> {
    const dataToCreate = {
      project_name: projectData.project_name,
      description: projectData.description || null,
      teacher_id: projectData.teacher_id
    }

    return this.create(dataToCreate)
  }

  // 更新项目
  async updateProject(projectId: number, projectData: UpdateProjectData): Promise<Project | null> {
    const updateData: Partial<Project> = {}

    if (projectData.project_name !== undefined) {
      updateData.project_name = projectData.project_name
    }

    if (projectData.description !== undefined) {
      updateData.description = projectData.description
    }

    return this.update(projectId, updateData)
  }

  // 获取项目详情（包含教师信息）
  async findByIdWithTeacher(projectId: number): Promise<ProjectWithTeacher | null> {
    const sql = `
      SELECT 
        p.*,
        u.full_name as teacher_name,
        u.email as teacher_email
      FROM projects p
      JOIN users u ON p.teacher_id = u.user_id
      WHERE p.project_id = ?
      LIMIT 1
    `
    return safeExecuteQuerySingle<ProjectWithTeacher>(sql, [projectId])
  }

  // 获取教师的项目列表（包含统计信息）
  async findByTeacherIdWithStats(teacherId: number): Promise<ProjectStats[]> {
    const sql = `
      SELECT
        p.project_id,
        p.project_name,
        p.description,
        p.created_at,
        COUNT(DISTINCT sp.student_id) as student_count,
        COUNT(DISTINCT t.task_id) as task_count
      FROM projects p
      LEFT JOIN student_projects sp ON p.project_id = sp.project_id
      LEFT JOIN tasks t ON p.project_id = t.project_id
      WHERE p.teacher_id = ?
      GROUP BY p.project_id, p.project_name, p.description, p.created_at
      ORDER BY p.created_at DESC
    `
    return safeExecuteQuery<ProjectStats>(sql, [teacherId])
  }

  // 获取学生参与的项目
  async findByStudentId(studentId: number): Promise<ProjectWithTeacher[]> {
    const sql = `
      SELECT 
        p.*,
        u.full_name as teacher_name,
        u.email as teacher_email
      FROM projects p
      JOIN users u ON p.teacher_id = u.user_id
      JOIN student_projects sp ON p.project_id = sp.project_id
      WHERE sp.student_id = ?
      ORDER BY p.created_at DESC
    `
    return safeExecuteQuery<ProjectWithTeacher>(sql, [studentId])
  }

  // 分配学生到项目
  async assignStudentsToProject(projectId: number, studentIds: number[]): Promise<boolean> {
    return safeExecuteTransaction(async (connection) => {
      // 首先删除现有的分配
      const deleteSql = 'DELETE FROM student_projects WHERE project_id = ?'
      await connection.execute(deleteSql, [projectId])

      // 然后添加新的分配
      if (studentIds.length > 0) {
        const insertSql = 'INSERT INTO student_projects (project_id, student_id) VALUES ?'
        const values = studentIds.map(studentId => [projectId, studentId])
        await connection.query(insertSql, [values])
      }

      return true
    })
  }

  // 添加学生到项目
  async addStudentToProject(projectId: number, studentId: number): Promise<boolean> {
    const sql = 'INSERT INTO student_projects (project_id, student_id) VALUES (?, ?)'
    try {
      await safeExecuteQuery(sql, [projectId, studentId])
      return true
    } catch (error: any) {
      // 如果是重复键错误，返回false
      if (error.code === 'ER_DUP_ENTRY') {
        return false
      }
      throw error
    }
  }

  // 从项目中移除学生
  async removeStudentFromProject(projectId: number, studentId: number): Promise<boolean> {
    const sql = 'DELETE FROM student_projects WHERE project_id = ? AND student_id = ?'
    const result = await safeExecuteQuery(sql, [projectId, studentId]) as any
    return result.affectedRows > 0
  }

  // 获取项目的学生列表
  async getProjectStudents(projectId: number): Promise<Array<{
    user_id: number
    username: string
    full_name: string | null
    email: string
    git_repo_url: string | null
  }>> {
    const sql = `
      SELECT 
        u.user_id,
        u.username,
        u.full_name,
        u.email,
        sp.git_repo_url
      FROM users u
      JOIN student_projects sp ON u.user_id = sp.student_id
      WHERE sp.project_id = ?
      ORDER BY u.full_name, u.username
    `
    return safeExecuteQuery(sql, [projectId])
  }

  // 更新学生的Git仓库地址
  async updateStudentGitRepo(projectId: number, studentId: number, gitRepoUrl: string): Promise<boolean> {
    const sql = 'UPDATE student_projects SET git_repo_url = ? WHERE project_id = ? AND student_id = ?'
    const result = await safeExecuteQuery(sql, [gitRepoUrl, projectId, studentId]) as any
    return result.affectedRows > 0
  }

  // 获取学生在项目中的Git仓库地址
  async getStudentGitRepo(projectId: number, studentId: number): Promise<string | null> {
    const sql = 'SELECT git_repo_url FROM student_projects WHERE project_id = ? AND student_id = ?'
    const result = await safeExecuteQuerySingle<{ git_repo_url: string | null }>(sql, [projectId, studentId])
    return result?.git_repo_url || null
  }

  // 获取学生在项目中的完整信息
  async getStudentProjectInfo(studentId: number, projectId: number): Promise<{ git_repo_url: string | null } | null> {
    const sql = 'SELECT git_repo_url FROM student_projects WHERE student_id = ? AND project_id = ?'
    const result = await safeExecuteQuerySingle<{ git_repo_url: string | null }>(sql, [studentId, projectId])
    return result
  }

  // 搜索项目
  async searchProjects(query: string, teacherId?: number): Promise<ProjectWithTeacher[]> {
    let sql = `
      SELECT 
        p.*,
        u.full_name as teacher_name,
        u.email as teacher_email
      FROM projects p
      JOIN users u ON p.teacher_id = u.user_id
      WHERE (p.project_name LIKE ? OR p.description LIKE ?)
    `
    const params = [`%${query}%`, `%${query}%`]

    if (teacherId) {
      sql += ' AND p.teacher_id = ?'
      params.push(String(teacherId))
    }

    sql += ' ORDER BY p.created_at DESC LIMIT 50'

    return safeExecuteQuery<ProjectWithTeacher>(sql, params)
  }

  // 检查学生是否参与项目
  async isStudentInProject(projectId: number, studentId: number): Promise<boolean> {
    const sql = 'SELECT 1 FROM student_projects WHERE project_id = ? AND student_id = ? LIMIT 1'
    const result = await safeExecuteQuerySingle(sql, [projectId, studentId])
    return result !== null
  }

  // 获取项目统计信息
  async getProjectStats(): Promise<{
    total_projects: number
    active_projects: number
    total_students_enrolled: number
  }> {
    const sql = `
      SELECT 
        COUNT(DISTINCT p.project_id) as total_projects,
        COUNT(DISTINCT CASE WHEN sp.student_id IS NOT NULL THEN p.project_id END) as active_projects,
        COUNT(DISTINCT sp.student_id) as total_students_enrolled
      FROM projects p
      LEFT JOIN student_projects sp ON p.project_id = sp.project_id
    `
    
    const result = await safeExecuteQuerySingle<{
      total_projects: number
      active_projects: number
      total_students_enrolled: number
    }>(sql)
    
    return result || {
      total_projects: 0,
      active_projects: 0,
      total_students_enrolled: 0
    }
  }

  // 删除项目（级联删除相关数据）
  async deleteProject(projectId: number): Promise<boolean> {
    return safeExecuteTransaction(async (connection) => {
      // 删除学生-项目关联
      await connection.execute('DELETE FROM student_projects WHERE project_id = ?', [projectId])
      
      // 删除项目相关的任务和提交（如果需要的话，这里可以添加更多清理逻辑）
      // 注意：由于外键约束，可能需要先删除相关的任务和提交
      
      // 删除项目
      const result = await connection.execute('DELETE FROM projects WHERE project_id = ?', [projectId])
      return (result[0] as any).affectedRows > 0
    })
  }
}
