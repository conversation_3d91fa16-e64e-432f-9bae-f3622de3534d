import { requireStudent } from '../../../utils/auth-server'
import { SubmissionRepository } from '../../../repositories/submission'

// 学生提交历史响应接口
interface StudentSubmissionsResponse {
  success: boolean
  data: Array<{
    submission_id: number
    task_id: number
    task_title: string
    project_name: string
    progress_description: string
    git_commit_hash: string | null
    submission_date: string
    status: string
    git_repo_url: string | null
  }>
}

export default defineEventHandler(async (event): Promise<StudentSubmissionsResponse> => {
  try {
    // 验证学生权限
    const student = await requireStudent(event)

    // 获取查询参数
    const query = getQuery(event)
    const projectId = query.project_id ? parseInt(query.project_id as string) : undefined
    const taskId = query.task_id ? parseInt(query.task_id as string) : undefined
    const status = query.status as string | undefined

    // 初始化仓储
    const submissionRepository = new SubmissionRepository()

    // 获取学生的提交历史
    let submissions = await submissionRepository.findByStudentId(student.user_id)

    // 根据项目ID过滤
    if (projectId) {
      submissions = submissions.filter(submission => {
        // 需要通过task关联到project，这里假设SubmissionWithDetails包含project信息
        return submission.project_name // 这个字段应该在SubmissionWithDetails中
      })
    }

    // 根据任务ID过滤
    if (taskId) {
      submissions = submissions.filter(submission => submission.task_id === taskId)
    }

    // 根据状态过滤
    if (status) {
      submissions = submissions.filter(submission => submission.status === status)
    }

    // 格式化响应数据
    const formattedSubmissions = submissions.map(submission => ({
      submission_id: submission.submission_id,
      task_id: submission.task_id,
      task_title: submission.task_title,
      project_name: submission.project_name,
      progress_description: submission.progress_description,
      git_commit_hash: submission.git_commit_hash,
      submission_date: submission.submission_date.toISOString(),
      status: submission.status,
      git_repo_url: submission.git_repo_url
    }))

    return {
      success: true,
      data: formattedSubmissions
    }
  } catch (error) {
    console.error('Student submissions API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      data: []
    }
  }
})
