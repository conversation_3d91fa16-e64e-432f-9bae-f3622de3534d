import { requireStudent } from '../../../../utils/auth-server'
import { ProjectRepository } from '../../../../repositories/project'
import { TaskRepository } from '../../../../repositories/task'

// 学生项目详情响应接口
interface StudentProjectDetailResponse {
  success: boolean
  data?: {
    project_id: number
    project_name: string
    description: string | null
    teacher_name: string
    teacher_email: string
    git_repo_url: string | null
    created_at: string
    tasks: Array<{
      task_id: number
      title: string
      description: string | null
      due_date: string | null
      created_at: string
      submission_status: string
      latest_submission_id: number | null
      latest_submission_date: string | null
    }>
  }
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<StudentProjectDetailResponse> => {
  try {
    // 验证学生权限
    const student = await requireStudent(event)

    // 获取项目ID
    const projectId = parseInt(getRouterParam(event, 'id') || '0')
    if (!projectId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'INVALID_PROJECT_ID',
          message: '无效的项目ID'
        }
      }
    }

    // 初始化仓储
    const projectRepository = new ProjectRepository()
    const taskRepository = new TaskRepository()

    // 获取项目详情（包含教师信息）
    const project = await projectRepository.findByIdWithTeacher(projectId)
    if (!project) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'PROJECT_NOT_FOUND',
          message: '项目不存在'
        }
      }
    }

    // 验证学生是否参与此项目
    const studentProjects = await projectRepository.findByStudentId(student.user_id)
    const isParticipant = studentProjects.some(p => p.project_id === projectId)
    
    if (!isParticipant) {
      setResponseStatus(event, 403)
      return {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '无权访问此项目'
        }
      }
    }

    // 获取项目的任务列表（包含学生的提交状态）
    const allTasks = await taskRepository.findByStudentId(student.user_id)
    const projectTasks = allTasks.filter(task => task.project_id === projectId)

    // 获取学生在此项目中的Git仓库URL
    const studentProject = await projectRepository.getStudentProjectInfo(student.user_id, projectId)

    // 格式化任务数据
    const formattedTasks = projectTasks.map(task => ({
      task_id: task.task_id,
      title: task.title,
      description: task.description,
      due_date: task.due_date ? task.due_date.toISOString() : null,
      created_at: task.created_at.toISOString(),
      submission_status: task.submission_status,
      latest_submission_id: task.latest_submission_id,
      latest_submission_date: task.latest_submission_date ? task.latest_submission_date.toISOString() : null
    }))

    return {
      success: true,
      data: {
        project_id: project.project_id,
        project_name: project.project_name,
        description: project.description,
        teacher_name: project.teacher_name,
        teacher_email: project.teacher_email,
        git_repo_url: studentProject?.git_repo_url || null,
        created_at: project.created_at.toISOString(),
        tasks: formattedTasks
      }
    }
  } catch (error) {
    console.error('Student project detail API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '获取项目详情时发生错误'
      }
    }
  }
})
