import { requireStudent } from '../../../../../utils/auth-server'
import { ProjectRepository } from '../../../../../repositories/project'
import { z } from 'zod'

// 更新Git仓库地址请求验证
const updateRepoSchema = z.object({
  git_repo_url: z.string().url('请输入有效的Git仓库URL').min(1, 'Git仓库URL不能为空')
})

// 更新Git仓库地址响应接口
interface UpdateRepoResponse {
  success: boolean
  data?: {
    project_id: number
    git_repo_url: string
  }
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<UpdateRepoResponse> => {
  try {
    // 验证学生权限
    const student = await requireStudent(event)

    // 获取项目ID
    const projectId = parseInt(getRouterParam(event, 'id') || '0')
    if (!projectId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'INVALID_PROJECT_ID',
          message: '无效的项目ID'
        }
      }
    }

    // 获取请求体
    const body = await readBody(event)

    // 验证请求数据
    const validation = updateRepoSchema.safeParse(body)
    if (!validation.success) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: validation.error.errors[0]?.message || '请求数据格式错误'
        }
      }
    }

    const { git_repo_url } = validation.data

    // 初始化仓储
    const projectRepository = new ProjectRepository()

    // 验证项目是否存在
    const project = await projectRepository.findById(projectId)
    if (!project) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'PROJECT_NOT_FOUND',
          message: '项目不存在'
        }
      }
    }

    // 验证学生是否参与此项目
    const isParticipant = await projectRepository.isStudentInProject(projectId, student.user_id)
    if (!isParticipant) {
      setResponseStatus(event, 403)
      return {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '无权修改此项目的Git仓库地址'
        }
      }
    }

    // 更新Git仓库地址
    const updateResult = await projectRepository.updateStudentGitRepo(projectId, student.user_id, git_repo_url)
    if (!updateResult) {
      setResponseStatus(event, 500)
      return {
        success: false,
        error: {
          code: 'UPDATE_FAILED',
          message: '更新Git仓库地址失败'
        }
      }
    }

    return {
      success: true,
      data: {
        project_id: projectId,
        git_repo_url: git_repo_url
      }
    }
  } catch (error) {
    console.error('Update repo API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '更新Git仓库地址时发生错误'
      }
    }
  }
})
