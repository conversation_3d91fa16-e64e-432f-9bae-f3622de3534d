import { requireStudent } from '../../../utils/auth-server'
import { SubmissionRepository } from '../../../repositories/submission'
import { TaskRepository } from '../../../repositories/task'
import { ProjectRepository } from '../../../repositories/project'
import { z } from 'zod'

// 创建提交请求验证
const createSubmissionSchema = z.object({
  task_id: z.number().int().positive('任务ID必须是正整数'),
  progress_description: z.string().min(1, '进度描述不能为空').max(2000, '进度描述不能超过2000字符'),
  git_commit_hash: z.string().min(1, 'Git提交哈希不能为空').max(100, 'Git提交哈希格式错误')
})

// 创建提交响应接口
interface CreateSubmissionResponse {
  success: boolean
  data?: {
    submission_id: number
    task_id: number
    progress_description: string
    git_commit_hash: string
    submission_date: string
    status: string
  }
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<CreateSubmissionResponse> => {
  try {
    // 验证学生权限
    const student = await requireStudent(event)

    // 获取请求体
    const body = await readBody(event)

    // 验证请求数据
    const validation = createSubmissionSchema.safeParse(body)
    if (!validation.success) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: validation.error.errors[0]?.message || '请求数据格式错误'
        }
      }
    }

    const { task_id, progress_description, git_commit_hash } = validation.data

    // 初始化仓储
    const submissionRepository = new SubmissionRepository()
    const taskRepository = new TaskRepository()
    const projectRepository = new ProjectRepository()

    // 验证任务是否存在
    const task = await taskRepository.findByIdWithProject(task_id)
    if (!task) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'TASK_NOT_FOUND',
          message: '任务不存在'
        }
      }
    }

    // 验证学生是否参与此项目
    const isParticipant = await projectRepository.isStudentInProject(task.project_id, student.user_id)
    if (!isParticipant) {
      setResponseStatus(event, 403)
      return {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '无权提交此任务'
        }
      }
    }

    // 检查是否已有提交记录
    const existingSubmissions = await submissionRepository.findByTaskIdAndStudentId(task_id, student.user_id)
    
    let submission
    if (existingSubmissions.length > 0) {
      // 更新现有提交
      const latestSubmission = existingSubmissions[0]
      submission = await submissionRepository.updateSubmission(latestSubmission.submission_id, {
        progress_description,
        git_commit_hash,
        status: 'submitted'
      })
    } else {
      // 创建新提交
      submission = await submissionRepository.createSubmission({
        task_id,
        student_id: student.user_id,
        progress_description,
        git_commit_hash,
        status: 'submitted'
      })
    }

    if (!submission) {
      setResponseStatus(event, 500)
      return {
        success: false,
        error: {
          code: 'SUBMISSION_FAILED',
          message: '提交失败'
        }
      }
    }

    return {
      success: true,
      data: {
        submission_id: submission.submission_id,
        task_id: submission.task_id,
        progress_description: submission.progress_description,
        git_commit_hash: submission.git_commit_hash || '',
        submission_date: submission.submission_date.toISOString(),
        status: submission.status
      }
    }
  } catch (error) {
    console.error('Create submission API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '提交时发生错误'
      }
    }
  }
})
