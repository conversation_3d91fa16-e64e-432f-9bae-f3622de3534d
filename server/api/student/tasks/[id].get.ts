import { requireStudent } from '../../../../utils/auth-server'
import { TaskRepository } from '../../../../repositories/task'
import { SubmissionRepository } from '../../../../repositories/submission'
import { FeedbackRepository } from '../../../../repositories/feedback'
import { ProjectRepository } from '../../../../repositories/project'

// 学生任务详情响应接口
interface StudentTaskDetailResponse {
  success: boolean
  data?: {
    task_id: number
    title: string
    description: string | null
    due_date: string | null
    created_at: string
    project_id: number
    project_name: string
    teacher_name: string
    git_repo_url: string | null
    submission_status: string
    latest_submission: {
      submission_id: number
      progress_description: string
      git_commit_hash: string | null
      submission_date: string
      status: string
    } | null
    feedback: Array<{
      feedback_id: number
      comment: string
      created_at: string
      teacher_name: string
    }>
  }
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<StudentTaskDetailResponse> => {
  try {
    // 验证学生权限
    const student = await requireStudent(event)

    // 获取任务ID
    const taskId = parseInt(getRouterParam(event, 'id') || '0')
    if (!taskId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'INVALID_TASK_ID',
          message: '无效的任务ID'
        }
      }
    }

    // 初始化仓储
    const taskRepository = new TaskRepository()
    const submissionRepository = new SubmissionRepository()
    const feedbackRepository = new FeedbackRepository()
    const projectRepository = new ProjectRepository()

    // 获取任务详情（包含项目信息）
    const task = await taskRepository.findByIdWithProject(taskId)
    if (!task) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'TASK_NOT_FOUND',
          message: '任务不存在'
        }
      }
    }

    // 验证学生是否参与此项目
    const isParticipant = await projectRepository.isStudentInProject(task.project_id, student.user_id)
    if (!isParticipant) {
      setResponseStatus(event, 403)
      return {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '无权访问此任务'
        }
      }
    }

    // 获取学生在此项目中的Git仓库URL
    const gitRepoUrl = await projectRepository.getStudentGitRepo(task.project_id, student.user_id)

    // 获取学生的提交记录
    const submissions = await submissionRepository.findByTaskIdAndStudentId(taskId, student.user_id)
    const latestSubmission = submissions.length > 0 ? submissions[0] : null

    // 获取反馈记录
    let feedback: any[] = []
    if (latestSubmission) {
      feedback = await feedbackRepository.findBySubmissionId(latestSubmission.submission_id)
    }

    // 确定提交状态
    let submissionStatus = 'not_submitted'
    if (latestSubmission) {
      submissionStatus = latestSubmission.status
    }

    // 格式化反馈数据
    const formattedFeedback = feedback.map(f => ({
      feedback_id: f.feedback_id,
      comment: f.comment,
      created_at: f.created_at.toISOString(),
      teacher_name: f.teacher_name
    }))

    return {
      success: true,
      data: {
        task_id: task.task_id,
        title: task.title,
        description: task.description,
        due_date: task.due_date ? task.due_date.toISOString() : null,
        created_at: task.created_at.toISOString(),
        project_id: task.project_id,
        project_name: task.project_name,
        teacher_name: task.teacher_name,
        git_repo_url: gitRepoUrl,
        submission_status: submissionStatus,
        latest_submission: latestSubmission ? {
          submission_id: latestSubmission.submission_id,
          progress_description: latestSubmission.progress_description,
          git_commit_hash: latestSubmission.git_commit_hash,
          submission_date: latestSubmission.submission_date.toISOString(),
          status: latestSubmission.status
        } : null,
        feedback: formattedFeedback
      }
    }
  } catch (error) {
    console.error('Student task detail API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '获取任务详情时发生错误'
      }
    }
  }
})
