import { requireStudent } from '../../../utils/auth-server'
import { ProjectRepository } from '../../../repositories/project'
import { TaskRepository } from '../../../repositories/task'
import { SubmissionRepository } from '../../../repositories/submission'
import { FeedbackRepository } from '../../../repositories/feedback'

// 学生仪表盘响应接口
interface StudentDashboardResponse {
  success: boolean
  data: {
    tasks: {
      in_progress: Array<{
        task_id: number
        title: string
        description: string | null
        due_date: string | null
        project_name: string
        submission_status: string
      }>
      needs_revision: Array<{
        task_id: number
        title: string
        description: string | null
        due_date: string | null
        project_name: string
        submission_status: string
      }>
    }
    recent_submissions: Array<{
      submission_id: number
      task_title: string
      project_name: string
      submission_date: string
      status: string
    }>
    recent_feedback: Array<{
      feedback_id: number
      task_title: string
      project_name: string
      comment: string
      created_at: string
      teacher_name: string
    }>
    upcoming_deadlines: Array<{
      task_id: number
      title: string
      due_date: string
      project_name: string
      days_remaining: number
    }>
    summary: {
      total_projects: number
      in_progress_tasks: number
      completed_tasks: number
      pending_feedback: number
    }
  }
}

export default defineEventHandler(async (event): Promise<StudentDashboardResponse> => {
  try {
    // 验证学生权限
    const student = await requireStudent(event)

    // 初始化仓储
    const projectRepository = new ProjectRepository()
    const taskRepository = new TaskRepository()
    const submissionRepository = new SubmissionRepository()
    const feedbackRepository = new FeedbackRepository()

    // 获取学生的任务列表（包含提交状态）
    const allTasks = await taskRepository.findByStudentId(student.user_id)

    // 分类任务
    const inProgressTasks = allTasks.filter(task => 
      task.submission_status === 'in_progress' || task.submission_status === 'not_submitted'
    )
    const needsRevisionTasks = allTasks.filter(task => 
      task.submission_status === 'needs_revision'
    )

    // 获取学生的最近提交（最近5条）
    const recentSubmissions = await submissionRepository.findByStudentId(student.user_id)
    const limitedSubmissions = recentSubmissions.slice(0, 5)

    // 获取学生的最近反馈（最近5条）
    const recentFeedback = await feedbackRepository.findByStudentId(student.user_id)
    const limitedFeedback = recentFeedback.slice(0, 5)

    // 获取即将到期的任务（7天内）
    const now = new Date()
    const sevenDaysLater = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    
    const upcomingDeadlines = allTasks
      .filter(task => {
        if (!task.due_date) return false
        const dueDate = new Date(task.due_date)
        return dueDate >= now && dueDate <= sevenDaysLater && 
               (task.submission_status === 'not_submitted' || task.submission_status === 'in_progress')
      })
      .map(task => {
        const dueDate = new Date(task.due_date!)
        const timeDiff = dueDate.getTime() - now.getTime()
        const daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24))
        
        return {
          task_id: task.task_id,
          title: task.title,
          due_date: task.due_date!.toISOString(),
          project_name: task.project_name,
          days_remaining: daysRemaining
        }
      })
      .sort((a, b) => a.days_remaining - b.days_remaining)

    // 获取学生参与的项目数量
    const projects = await projectRepository.findByStudentId(student.user_id)
    
    // 计算统计数据
    const completedTasks = allTasks.filter(task => task.submission_status === 'completed').length
    const pendingFeedback = allTasks.filter(task => task.submission_status === 'submitted').length

    // 格式化响应数据
    const formattedInProgressTasks = inProgressTasks.map(task => ({
      task_id: task.task_id,
      title: task.title,
      description: task.description,
      due_date: task.due_date ? task.due_date.toISOString() : null,
      project_name: task.project_name,
      submission_status: task.submission_status
    }))

    const formattedNeedsRevisionTasks = needsRevisionTasks.map(task => ({
      task_id: task.task_id,
      title: task.title,
      description: task.description,
      due_date: task.due_date ? task.due_date.toISOString() : null,
      project_name: task.project_name,
      submission_status: task.submission_status
    }))

    const formattedRecentSubmissions = limitedSubmissions.map(submission => ({
      submission_id: submission.submission_id,
      task_title: submission.task_title,
      project_name: submission.project_name,
      submission_date: submission.submission_date.toISOString(),
      status: submission.status
    }))

    const formattedRecentFeedback = limitedFeedback.map(feedback => ({
      feedback_id: feedback.feedback_id,
      task_title: feedback.task_title,
      project_name: feedback.project_name,
      comment: feedback.comment,
      created_at: feedback.created_at.toISOString(),
      teacher_name: feedback.teacher_name
    }))

    return {
      success: true,
      data: {
        tasks: {
          in_progress: formattedInProgressTasks,
          needs_revision: formattedNeedsRevisionTasks
        },
        recent_submissions: formattedRecentSubmissions,
        recent_feedback: formattedRecentFeedback,
        upcoming_deadlines: upcomingDeadlines,
        summary: {
          total_projects: projects.length,
          in_progress_tasks: inProgressTasks.length,
          completed_tasks: completedTasks,
          pending_feedback: pendingFeedback
        }
      }
    }
  } catch (error) {
    console.error('Student dashboard API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      data: {
        tasks: { in_progress: [], needs_revision: [] },
        recent_submissions: [],
        recent_feedback: [],
        upcoming_deadlines: [],
        summary: {
          total_projects: 0,
          in_progress_tasks: 0,
          completed_tasks: 0,
          pending_feedback: 0
        }
      }
    }
  }
})
