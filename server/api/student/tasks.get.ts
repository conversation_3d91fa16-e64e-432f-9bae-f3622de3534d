import { requireStudent } from '../../../utils/auth-server'
import { TaskRepository } from '../../../repositories/task'

// 学生任务列表响应接口
interface StudentTasksResponse {
  success: boolean
  data: Array<{
    task_id: number
    title: string
    description: string | null
    due_date: string | null
    created_at: string
    project_id: number
    project_name: string
    teacher_name: string
    submission_status: string
    latest_submission_id: number | null
    latest_submission_date: string | null
  }>
}

export default defineEventHandler(async (event): Promise<StudentTasksResponse> => {
  try {
    // 验证学生权限
    const student = await requireStudent(event)

    // 获取查询参数
    const query = getQuery(event)
    const projectId = query.project_id ? parseInt(query.project_id as string) : undefined
    const status = query.status as string | undefined

    // 初始化仓储
    const taskRepository = new TaskRepository()

    // 获取学生的任务列表（包含提交状态）
    let tasks = await taskRepository.findByStudentId(student.user_id)

    // 根据项目ID过滤
    if (projectId) {
      tasks = tasks.filter(task => task.project_id === projectId)
    }

    // 根据状态过滤
    if (status) {
      switch (status) {
        case 'not_submitted':
          tasks = tasks.filter(task => task.submission_status === 'not_submitted')
          break
        case 'in_progress':
          tasks = tasks.filter(task => task.submission_status === 'in_progress')
          break
        case 'submitted':
          tasks = tasks.filter(task => task.submission_status === 'submitted')
          break
        case 'completed':
          tasks = tasks.filter(task => task.submission_status === 'completed')
          break
        case 'needs_revision':
          tasks = tasks.filter(task => task.submission_status === 'needs_revision')
          break
      }
    }

    // 格式化响应数据
    const formattedTasks = tasks.map(task => ({
      task_id: task.task_id,
      title: task.title,
      description: task.description,
      due_date: task.due_date ? task.due_date.toISOString() : null,
      created_at: task.created_at.toISOString(),
      project_id: task.project_id,
      project_name: task.project_name,
      teacher_name: task.teacher_name,
      submission_status: task.submission_status,
      latest_submission_id: task.latest_submission_id,
      latest_submission_date: task.latest_submission_date ? task.latest_submission_date.toISOString() : null
    }))

    return {
      success: true,
      data: formattedTasks
    }
  } catch (error) {
    console.error('Student tasks API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      data: []
    }
  }
})
