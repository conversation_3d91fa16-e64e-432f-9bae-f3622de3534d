import { requireStudent } from '../../../utils/auth-server'
import { ProjectRepository } from '../../../repositories/project'

// 学生项目列表响应接口
interface StudentProjectsResponse {
  success: boolean
  data: Array<{
    project_id: number
    project_name: string
    description: string | null
    teacher_name: string
    teacher_email: string
    git_repo_url: string | null
    created_at: string
  }>
}

export default defineEventHandler(async (event): Promise<StudentProjectsResponse> => {
  try {
    // 验证学生权限
    const student = await requireStudent(event)

    // 初始化仓储
    const projectRepository = new ProjectRepository()

    // 获取学生参与的项目列表（包含教师信息）
    const projects = await projectRepository.findByStudentId(student.user_id)

    // 格式化响应数据
    const formattedProjects = projects.map(project => ({
      project_id: project.project_id,
      project_name: project.project_name,
      description: project.description,
      teacher_name: project.teacher_name,
      teacher_email: project.teacher_email,
      git_repo_url: project.git_repo_url || null,
      created_at: project.created_at.toISOString()
    }))

    return {
      success: true,
      data: formattedProjects
    }
  } catch (error) {
    console.error('Student projects API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      data: []
    }
  }
})
