import { requireTeacher } from '../../../utils/auth-server'
import { SubmissionRepository } from '../../../repositories/submission'

// 教师提交列表响应接口
interface TeacherSubmissionsResponse {
  success: boolean
  data?: Array<{
    submission_id: number
    task_id: number
    task_title: string
    student_id: number
    student_name: string
    student_email: string
    content: string | null
    file_url: string | null
    status: 'pending' | 'approved' | 'rejected'
    submitted_at: string
    reviewed_at: string | null
    feedback: string | null
    score: number | null
    project_id: number
    project_name: string
  }>
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<TeacherSubmissionsResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 获取查询参数
    const query = getQuery(event)
    const taskId = query.task_id ? parseInt(query.task_id as string) : undefined
    const projectId = query.project_id ? parseInt(query.project_id as string) : undefined
    const status = query.status as string | undefined

    // 初始化仓储
    const submissionRepository = new SubmissionRepository()

    // 获取提交列表
    const submissions = await submissionRepository.findByTeacherIdWithFilters(teacher.user_id, {
      taskId,
      projectId,
      status
    })

    // 格式化响应数据
    const formattedSubmissions = submissions.map(submission => ({
      submission_id: submission.submission_id,
      task_id: submission.task_id,
      task_title: submission.task_title,
      student_id: submission.student_id,
      student_name: submission.student_name,
      student_email: submission.student_email,
      content: submission.content,
      file_url: submission.file_url,
      status: submission.status,
      submitted_at: submission.submitted_at.toISOString(),
      reviewed_at: submission.reviewed_at ? submission.reviewed_at.toISOString() : null,
      feedback: submission.feedback,
      score: submission.score,
      project_id: submission.project_id,
      project_name: submission.project_name
    }))

    return {
      success: true,
      data: formattedSubmissions
    }
  } catch (error) {
    console.error('Teacher submissions API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '获取提交列表时发生错误'
      }
    }
  }
})
