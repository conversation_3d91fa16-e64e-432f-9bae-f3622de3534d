// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },

  // Compatibility date
  compatibilityDate: '2025-07-29',

  // Modules
  modules: ['@nuxt/ui', '@pinia/nuxt', '@nuxtjs/tailwindcss'],

  // Nuxt UI configuration
  ui: {
    global: true
  },

  // CSS configuration
  css: ['~/assets/css/main.css'],

  // TypeScript configuration
  typescript: {
    strict: false,
    typeCheck: false
  },
  
  // Runtime config for environment variables
  runtimeConfig: {
    // Private keys (only available on server-side)
    jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret-key',
    dbHost: process.env.DB_HOST || 'localhost',
    dbPort: process.env.DB_PORT || '3306',
    dbUser: process.env.DB_USER || 'root',
    dbPassword: process.env.DB_PASSWORD || '',
    dbName: process.env.DB_NAME || 'student_task_management',
    
    // Public keys (exposed to client-side)
    public: {
      apiBase: '/api'
    }
  },
  
  // Development server configuration
  devServer: {
    port: 3001
  },

  // Server-side rendering configuration
  ssr: true,
  
  // App configuration
  app: {
    head: {
      title: 'Student Task Management System',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'A comprehensive task management system for students and teachers' }
      ]
    }
  },
  
  // Vite configuration to fix UnoCSS VFS errors
  vite: {
    server: {
      fs: {
        allow: ['..']
      },
    }
  },

  // Nitro configuration for server
  nitro: {
    experimental: {
      wasm: true
    }
  }
})
